import { createIconifyIcon } from '@bjy-core/icons';

import './load.js';

const SvgAvatar1Icon = createIconifyIcon('svg:avatar-1');
const SvgAvatar2Icon = createIconifyIcon('svg:avatar-2');
const SvgAvatar3Icon = createIconifyIcon('svg:avatar-3');
const SvgAvatar4Icon = createIconifyIcon('svg:avatar-4');
const SvgNewDownloadIcon = createIconifyIcon('svg:download-new');
const SvgCardIcon = createIconifyIcon('svg:card');
const SvgBellIcon = createIconifyIcon('svg:bell');
const SvgCakeIcon = createIconifyIcon('svg:cake');
const SvgAntdvLogoIcon = createIconifyIcon('svg:antdv-logo');
const SvgPdfIcon = createIconifyIcon('svg:pdf');
const SvgCloseIcon = createIconifyIcon('svg:close-normal');
const SvgFileSuccessIcon = createIconifyIcon('svg:file-success');
const SvgFileErrorIcon = createIconifyIcon('svg:file-error');
const SvgFileDelIcon = createIconifyIcon('svg:file-del');
const SvgFileRenameIcon = createIconifyIcon('svg:file-rename');
const SvgTitleIconIcon = createIconifyIcon('svg:title-icon');
const SvgTreeShowIcon = createIconifyIcon('svg:tree-show');
const SvgConnectIcon = createIconifyIcon('svg:connect');
const SvgHangUpIcon = createIconifyIcon('svg:hangUp');

export {
  SvgAntdvLogoIcon,
  SvgAvatar1Icon,
  SvgAvatar2Icon,
  SvgAvatar3Icon,
  SvgAvatar4Icon,
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgCloseIcon,
  SvgConnectIcon,
  SvgFileDelIcon,
  SvgFileErrorIcon,
  SvgFileRenameIcon,
  SvgFileSuccessIcon,
  SvgHangUpIcon,
  SvgNewDownloadIcon,
  SvgPdfIcon,
  SvgTitleIconIcon,
  SvgTreeShowIcon,
};
