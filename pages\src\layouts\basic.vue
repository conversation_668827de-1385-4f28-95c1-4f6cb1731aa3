<script lang="ts" setup>
import type { NotificationItem } from '@bjy/layouts';

import { computed, ref, watch } from 'vue';

import { AuthenticationLoginExpiredModal } from '@bjy/common-ui';
import { useWatermark } from '@bjy/hooks';
import { ArrowsRightLeft, IconifyIcon } from '@bjy/icons';
import {
  BasicLayout,
  Breadcrumb,
  LockScreen,
  Notification,
  UserDropdown,
} from '@bjy/layouts';
import { preferences } from '@bjy/preferences';
import { useAccessStore, useUserStore } from '@bjy/stores';
import { openRouteInNewWindow } from '@bjy/utils';

import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';

const isPoleAdmin = import.meta.env.VITE_BASE === '/pole-admin/';
const notifications = ref<NotificationItem[]>([
  {
    avatar: 'https://avatar.vercel.sh/vercel.svg?text=VB',
    date: '3小时前',
    isRead: true,
    message: '描述信息描述信息描述信息',
    title: '收到了 14 份新周报',
  },
  {
    avatar: 'https://avatar.vercel.sh/1',
    date: '刚刚',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '朱偏右 回复了你',
  },
  {
    avatar: 'https://avatar.vercel.sh/1',
    date: '2024-01-01',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '曲丽丽 评论了你',
  },
  {
    avatar: 'https://avatar.vercel.sh/satori',
    date: '1天前',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '代办提醒',
  },
]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => [
  {
    handler: () => {
      openRouteInNewWindow('/', { target: '_parent' });
    },
    icon: ArrowsRightLeft,
    text: '切换门户',
  },
]);

const avatar = computed(() => {
  return userStore.userInfo?.user?.avatar ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
  await authStore.logout(false);
}

function handleNoticeClear() {
  notifications.value = [];
}

function handleMakeAll() {
  notifications.value.forEach((item) => (item.isRead = true));
}
// https://dev-drcloud.bhidi.com/pole-admin/  项目端
// https://dev-drcloud.bhidi.com/pole-manager-admin/  平台端
watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout :header-height="64" @clear-preferences-and-logout="handleLogout">
    <template #logo>
      <div class="logo-box">
        <IconifyIcon
          class="inline size-[25px]"
          icon="svg:title-icon"
        />
        <span class="title-name ml-2 text-[18px]">{{
          isPoleAdmin ? '怒江智能卡口一体杆管理系统' : '智能卡口一体杆管理平台'
        }}</span>
      </div>
    </template>

    <!-- 顶部菜单 -->
    <template v-if="false" #header-menu>222</template>
    <!-- 侧边栏菜单 -->
    <template v-if="false" #left-menu>33333</template>

    <!-- header 下边框 -->
    <template #header-left-100-line>
      <div class="dived-box flex h-[4px] w-[100%]">
        <div class="blue h-full w-[30%]"></div>
        <div class="white-box"></div>
      </div>
    </template>
    <!-- 面包屑 -->
    <template v-if="false" #header-left-90-breadcrumb>
      <Breadcrumb
        :show-home="false"
        :show-icon="false"
        hide-when-only-one
        type="normal"
      />
    </template>
    <!-- <template #header-right-140-extra>
      <div>扩展插槽</div>
    </template> -->

    <template #user-dropdown>
      <UserDropdown
        :avatar
        :enable-shortcut-key="false"
        :menus
        :text="userStore.userInfo?.user?.username"
        trigger="hover"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification
        :dot="showDot"
        :notifications="notifications"
        @clear="handleNoticeClear"
        @make-all="handleMakeAll"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
</template>

<style lang="scss" scoped>
// header背景
:deep(.bg-header) {
  // background
}

.dived-box {
  position: absolute;
  bottom: 0;
  background-color: #c70b4d;

  .blue {
    background: #4081e2;
  }

  .white-box {
    width: 8px;
    background: url('/src/assets/images/bjy_line.svg');
  }
}
@font-face {
  font-family: 'AlimamaShuHeiTi-Bold';
  src: url('@/assets/styles/AlimamaShuHeiTi-Bold.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

.logo-box {
  @apply ml-8 flex items-center;
}

.title-name {
  font-family: 'AlimamaShuHeiTi-Bold', sans-serif;
  color: #fff;
}
</style>
