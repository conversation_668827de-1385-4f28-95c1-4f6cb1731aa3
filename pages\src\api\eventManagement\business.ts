// 业务事件
import { requestClient } from '#/api/request';

/**
 * 获得事件管理分页
 */
export const getPoleDeviceEventPage = (params: any) => {
  return requestClient.get('/pole/device-event/page', { params });
};
/**
 * 更新事件管理
 */
export const putPoleDeviceEventUpdate = (params: any) => {
  return requestClient.put('/pole/device-event/update', params);
};
/**
 * 删除事件管理
 */
export const DeletePoleDeviceEventDelete = (params:any) => {
  return requestClient.delete(`/pole/device-event/delete`,{params});
};
/**
 * 事件处置
 */
export const postPoleDeviceEventDisposal = (params: any) => {
  return requestClient.post('/pole/device-event/disposal', params);
};
/**
 * 更新状态为已查看
 */
export const postPoleDeviceEventUpdateStatus = (params: any) => {
  return requestClient.post(`/pole/device-event/updateStatus?id=${params}`);
};
