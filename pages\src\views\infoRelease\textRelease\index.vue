<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { ColPage } from '@bjy/common-ui';

import {
  <PERSON><PERSON>,
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Spin,
  Switch,
  Textarea,
  TimePicker,
} from 'bhidi-design';
// @ts-ignore

import {
  createVoiceRelease,
  getVoiceReleaseDataCode,
  getVoiceReleaseDataId,
} from '#/api/infoRelease';
import { fetchRegionAndDevices } from '#/api/videoSurveillance';
import { Tree } from '#/components';

defineOptions({ name: 'TextRelease' });

const props = reactive({
  leftCollapsible: true,
  leftWidth: 20,
  rightWidth: 80,
  isInternal: true,
  resizable: true,
});

const labelCol = { style: { width: '80px' } };

const text = ref('请选择设备');

const treeLoading = ref<boolean>(true);

const loading = ref<boolean>(false);

const deviceCodeList = ref<any>([]);

const textList = ref<any>([
  {
    voiceName: '',
    status: 0,
    startTime: '',
    volume: 0,
    voiceContent: '',
    playSpeed: '',
  },
  {
    voiceName: '',
    status: 0,
    startTime: '',
    endTime: '',
    volume: 0,
    voiceContent: '',
    playSpeed: '',
  },
  {
    voiceName: '',
    status: 0,
    startTime: '',
    endTime: '',
    volume: 0,
    voiceContent: '',
    playSpeed: '',
  },
]);

const treeData = ref<any>([]);

const cloneData = ref<any>([]);

// 创建一个响应式数组来存储所有选中的节点
const selectedNodes = ref<any[]>([]);

// 定义递归函数，用于在树数据中查找匹配的节点
const findCodeInTree = (data: any[], targetKeys: number[]) => {
  const treeCodeValues: any[] = [];
  for (const node of data) {
    if (targetKeys.includes(node.id) && node.code) {
      treeCodeValues.push(node);
    }
    if (node.children) {
      // 合并子节点递归返回的结果
      treeCodeValues.push(...findCodeInTree(node.children, targetKeys));
    }
  }
  return treeCodeValues;
};

const onSelect = async (key: any, node: any) => {
  // 清空之前的选中节点（如果需要累积所有选择，可以移除这一行）
  selectedNodes.value = [];

  // 将新的节点添加到selectedNodes数组中
  if (Array.isArray(node)) {
    selectedNodes.value.push(...node);
  } else if (node) {
    selectedNodes.value.push(node);
  }

  if (key.length > 0) {
    loading.value = true;
    const res = node.leafNode
      ? await getVoiceReleaseDataCode({ divicecode: node.code, releaseType: 0 })
      : await getVoiceReleaseDataId({
          id: node.id,
          releaseType: 0,
        });

    cloneData.value = res;

    const data = Array.isArray(res) ? res.slice(-3) : res;

    const list = key.filter((item: any) => typeof item === 'number');

    const treeCodes = findCodeInTree(treeData.value, list);

    // 更新显示文本
    text.value =
      treeCodes.length > 0
        ? `已选择 ${treeCodes.length} 个设备：${treeCodes.map((item: any) => item.name).join('、')}`
        : '请选择设备';

    deviceCodeList.value =
      data.length > 0
        ? [...new Set(res.map((item: any) => item.deviceCode))]
        : [...new Set(treeCodes.map((item: any) => item.code))];

    textList.value = data.length > 0 ? data : textList.value;
    loading.value = false;
  } else {
    text.value = '请选择设备';
  }
};

const getTreeList = async () => {
  treeLoading.value = true;
  try {
    const res = await fetchRegionAndDevices();
    treeData.value = [res];
  } catch {
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

// 检查两个时间段是否交叉的函数
const isTimeOverlap = (
  start1: string,
  end1: string,
  start2: string,
  end2: string,
) => {
  // 将时间字符串转换为时间戳（秒）
  const toSeconds = (timeStr: string) => {
    const [hours, minutes, seconds] = timeStr.split(':').map(Number);
    return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
  };

  const s1 = toSeconds(start1);
  const e1 = toSeconds(end1);
  const s2 = toSeconds(start2);
  const e2 = toSeconds(end2);

  // 检查时间段是否交叉
  // 交叉的情况：s1 < e2 且 s2 < e1
  return s1 <= e2 && s2 <= e1;
};

// 检查时间交叉的函数
const hasTimeConflict = () => {
  // 直接使用所有语音配置，不考虑是否选中
  const voices = textList.value;

  // 如果语音配置少于2个，不可能有交叉
  if (voices.length < 2) {
    return false;
  }

  // 遍历所有语音配置，检查每对之间是否有时间交叉
  for (let i = 0; i < voices.length; i++) {
    const voice1 = voices[i];

    if (!voice1) continue;

    for (let j = i + 1; j < voices.length; j++) {
      const voice2 = voices[j];

      if (!voice2) continue;

      // 检查两个时间段是否交叉
      if (
        isTimeOverlap(
          voice1.startTime,
          voice1.endTime,
          voice2.startTime,
          voice2.endTime,
        )
      ) {
        return true;
      }
    }
  }
  return false;
};

const handleSave = async () => {
  // 检查所有语音配置是否都填写了时间，若都填写了才进行时间交叉校验
  const allHaveTime = textList.value.every(
    (item: any) => item.startTime && item.endTime,
  );
  if (allHaveTime && hasTimeConflict()) {
    return message.warning('存在时间交叉的语音配置，请调整时间段后再保存！');
  }
  const data: any[] = [];
  deviceCodeList.value.forEach((code: any) => {
    textList.value.forEach((item: any, index: number) => {
      data.push({
        indexContent: index + 1,
        voiceName: item.voiceName,
        status: item.status,
        startTime: item.startTime,
        endTime: item.endTime,
        soundVolume: item.soundVolume,
        playSpeed: item.playSpeed,
        voiceContent: item.voiceContent,
        releaseType: 0, // 1 语音 0 文字
        voiceFile: item.voiceFile,
        deviceCode: code,
      });
    });
  });
  try {
    await createVoiceRelease(data);
    message.success('保存成功！');
  } catch {
    message.error('保存失败！');
  }
};

onMounted(() => {
  getTreeList();
});
</script>

<template>
  <ColPage auto-content-height v-bind="props">
    <template #left>
      <Tree
        :checkable="true"
        :data="treeData"
        :tree-loading="treeLoading"
        @select="onSelect"
      />
    </template>
    <div class="right-box mb-3 ml-3 flex flex-col">
      <div class="right-fixed-box">
        <div class="mb-[20px]">
          <Alert :message="text" show-icon type="success" />
        </div>
        <div class="right-scroll flex flex-col">
          <Spin
            :spinning="loading"
            size="large"
            wrapper-class-name="spin-bg h-full w-full"
          >
            <div class="right-scroll-box flex h-full">
              <div
                v-for="(item, index) in textList"
                :key="index"
                class="box-content h-full w-[calc(100%/3)]"
              >
                <Card :title="`文字配置${index + 1}`" class="h-full">
                  <template #extra>
                    <Switch
                      v-model:checked="item.status"
                      :checked-value="1"
                      :un-checked-value="0"
                    />
                  </template>
                  <div>
                    <Form
                      :label-col="labelCol"
                      label-align="right"
                      label-width="100px"
                      layout="horizontal"
                    >
                      <Form.Item label="文字名称">
                        <Input
                          v-model:value="item.voiceName"
                          placeholder="请输入文字名称"
                        />
                      </Form.Item>
                      <Form.Item label="文字内容">
                        <Textarea
                          v-model:value="item.voiceContent"
                          :auto-size="{ minRows: 3 }"
                          max-length="256"
                          placeholder="请输入文字内容"
                        />
                      </Form.Item>
                      <Form.Item label="播放速度">
                        <InputNumber
                          v-model:value="item.playSpeed"
                          addon-after="S(秒)"
                          class="w-full"
                          placeholder="请输入播放速度"
                        />
                      </Form.Item>
                      <Form.Item label="时间段选择">
                        <div class="w-full">
                          <TimePicker
                            v-model:value="item.startTime"
                            class="mr-[6px] w-[48%]"
                            format="HH:mm"
                            value-format="HH:mm"
                          />
                          <TimePicker
                            v-model:value="item.endTime"
                            class="w-[48%]"
                            format="HH:mm"
                            value-format="HH:mm"
                          />
                        </div>
                      </Form.Item>
                    </Form>
                  </div>
                </Card>
              </div>
            </div>
          </Spin>
          <div class="mt-3 w-full text-center">
            <Button type="primary" @click="handleSave">保存</Button>
          </div>
        </div>
      </div>
    </div>
  </ColPage>
</template>
<style scoped lang="less">
.right-box {
  background: #fff;
  height: 100%;
  border-radius: 4px;
  padding: 12px 24px;

  .right-fixed-box {
    flex: 1;
  }

  .right-scroll {
    height: calc(100% - 60px);
  }

  .right-scroll-box {
    flex: 1;
  }
}

.box-content {
  margin-right: 12px;
}
.box-content:last-child {
  margin-right: 0;
}
</style>
