<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';

import { ColPage } from '@bjy/common-ui';

import { Empty, Popover, Spin, Switch } from 'bhidi-design';
import flvjs from 'flv.js';

import {
  closeVideo,
  fetchRealtimeVideo,
  fetchRegionAndDevices,
  keepAliveVideo,
} from '#/api/videoSurveillance';
import sxt from '#/assets/images/sxt.jpg';
import { Tree } from '#/components';

import GridView from '../../../components/GridView/index.vue';

defineOptions({ name: 'LiveVideo' });

const colPageProps = reactive({
  leftCollapsible: true,
  leftWidth: 20,
  rightWidth: 80,
  isInternal: true,
  resizable: true,
});

const treeData = ref<any[]>([]);
const treeValue = ref<any[]>([]);
const gridCount = ref<number>(1);
const treeLoading = ref<boolean>(false);
const checked = ref<boolean>(false);
const visible = ref<boolean>(false);

// 修改为数组，为每个视频维护独立的状态和播放器
const videoRefs = ref<(HTMLVideoElement | null)[]>([]);
const flvPlayers = ref<(any | null)[]>([]);
const loadingStates = ref<boolean[]>([]);
const errorStates = ref<boolean[]>([]);
const errorMessages = ref<string[]>([]);

// 防抖处理，避免快速切换导致的问题
let lastLoadTime = 0;
const DEBOUNCE_TIME = 500;

// 为每个视频创建独立的定时器存储
const videoTimers = ref<Map<string, any>>(new Map());

const videoTime = (url: string) => {
  const nameWithExt = url.split('/').pop();
  const name = nameWithExt ? nameWithExt.split('.')[0] : '';

  // 清除可能存在的旧定时器
  if (name && videoTimers.value.has(name)) {
    clearInterval(videoTimers.value.get(name));
  }

  // 设置5分钟定时器
  const timer = setInterval(
    async () => {
      await keepAliveVideo({ videoName: name });
    },
    1000 * 60 * 5, // 修改为5分钟
  );

  if (name) {
    videoTimers.value.set(name, timer);
  }
};

const onSelect = async (selections: any[], _option: any) => {
  if (_option.leafNode) {
    const now = Date.now();
    // 如果距离上次加载时间过短，不执行操作
    if (now - lastLoadTime < DEBOUNCE_TIME) {
      return;
    }
    lastLoadTime = now;

    const index = treeValue.value.findIndex((item) => item.id === _option.code);

    if (index === -1) {
      // 如果未选中，则添加到选中列表
      if (treeValue.value.length >= gridCount.value) {
        // 如果已达到宫格数限制，从头部删除一个，再从尾部追加
        treeValue.value.shift();
        // 清理对应的视频资源
        cleanupVideoResource(0);
      } else {
        // 扩展状态数组
        loadingStates.value.push(true);
        errorStates.value.push(false);
        errorMessages.value.push('');
      }
      treeValue.value.push(_option);
    } else {
      // 如果已选中，则取消选中
      treeValue.value.splice(index, 1);
      // 清理对应的视频资源
      cleanupVideoResource(index);
    }

    // 获取当前要加载的视频索引
    const loadIndex = treeValue.value.length - 1;
    if (loadIndex >= 0) {
      loadingStates.value[loadIndex] = true;
      errorStates.value[loadIndex] = false;

      try {
        const res = await fetchRealtimeVideo({
          deviceCode: treeValue.value[loadIndex].code,
        });
        treeValue.value[loadIndex].videoUrl = res;
        await nextTick();
        createVideo(loadIndex, res);
        videoTime(res);
      } catch (error) {
        loadingStates.value[loadIndex] = false;
        errorStates.value[loadIndex] = true;
        errorMessages.value[loadIndex] = '获取视频流失败';
        console.error(error);
      }
    }
  }
};

const createVideo = async (index: number, url: string) => {
  // 确保索引有效
  if (index < 0 || index >= treeValue.value.length) return;

  // 清理可能存在的旧播放器
  cleanupVideoResource(index);

  if (flvjs.isSupported()) {
    try {
      flvPlayers.value[index] = flvjs.createPlayer({
        type: 'flv', // 必须为 'flv'
        isLive: true, // 直播流
        url, // 视频流地址
        hasAudio: false, // 根据实际情况调整
        hasVideo: true,
      });

      const videoElement = videoRefs.value[index];
      if (!videoElement) {
        loadingStates.value[index] = false;
        errorStates.value[index] = true;
        errorMessages.value[index] = '视频元素未找到';
        return;
      }

      // 附加到 video 元素 加载并播放
      flvPlayers.value[index]?.attachMediaElement(videoElement);

      // 监听播放器错误事件
      flvPlayers.value[index].on('error', (error: any) => {
        console.error(`FLV播放错误 [${index}]:`, error);
        loadingStates.value[index] = false;
        errorStates.value[index] = true;
        errorMessages.value[index] = 'FLV视频播放失败';
      });

      // 监听加载状态
      flvPlayers.value[index].on('loadedmetadata', () => {
        loadingStates.value[index] = false;
      });

      // 开始加载
      await flvPlayers.value[index]?.load();
      // 延迟播放，避免触发浏览器自动播放策略限制
      setTimeout(() => {
        if (flvPlayers.value[index]) {
          try {
            flvPlayers.value[index].play().catch((error) => {
              console.error(`播放失败 [${index}]:`, error);
              // 失败时不设置错误状态，因为视频可能仍在加载
            });
          } catch (error) {
            console.error(`播放异常 [${index}]:`, error);
          }
        }
      }, 100);
    } catch (error) {
      loadingStates.value[index] = false;
      errorStates.value[index] = true;
      errorMessages.value[index] = '视频初始化失败';
      console.error(`创建播放器失败 [${index}]:`, error);
    }
  } else {
    loadingStates.value[index] = false;
    errorStates.value[index] = true;
    errorMessages.value[index] = '浏览器不支持FLV格式';
  }
};

// 清理单个视频资源
const cleanupVideoResource = async (index: number) => {
  if (flvPlayers.value[index]) {
    try {
      flvPlayers.value[index].pause();
      flvPlayers.value[index].unload();
      flvPlayers.value[index].detachMediaElement();
      flvPlayers.value[index].destroy();

      const videoUrl = treeValue.value[index].videoUrl;
      const nameWithExt = videoUrl.split('/').pop();
      const name = nameWithExt ? nameWithExt.split('.')[0] : '';
      await closeVideo({ videoName: name });

      // 清除对应的定时器
      if (videoTimers.value.has(name)) {
        clearInterval(videoTimers.value.get(name));
        videoTimers.value.delete(name);
      }

      // 保存需要重新初始化的视频数据
      const videosToReinitialize: any[] = [];
      // 从index+1开始，保存所有后续视频的数据
      for (let i = index + 1; i < treeValue.value.length; i++) {
        videosToReinitialize.push({
          deviceCode: treeValue.value[i].code,
          targetIndex: i - 1, // 新的索引位置
        });
      }

      // 移除指定索引的所有相关数据
      treeValue.value.splice(index, 1);
      loadingStates.value.splice(index, 1);
      errorStates.value.splice(index, 1);
      errorMessages.value.splice(index, 1);
      flvPlayers.value.splice(index, 1);

      // 清空videoRefs，让Vue重新建立引用
      videoRefs.value = [];

      // 在下一个渲染周期重新初始化后续视频
      nextTick(() => {
        videosToReinitialize.forEach(async (item, i) => {
          const newIndex = item.targetIndex;
          loadingStates.value[newIndex] = true;
          errorStates.value[newIndex] = false;

          try {
            // 注意：这里使用真实的API调用获取视频流
            const res = await fetchRealtimeVideo({
              deviceCode: item.deviceCode,
            });
            await nextTick();
            createVideo(newIndex, res);
          } catch (error) {
            loadingStates.value[newIndex] = false;
            errorStates.value[newIndex] = true;
            errorMessages.value[newIndex] = '获取视频流失败';
            console.error(`重新初始化视频失败 [${newIndex}]:`, error);
          }
        });
      });
    } catch (error) {
      console.warn(`清理播放器资源出错 [${index}]:`, error);
    }
    flvPlayers.value[index] = null;
  }

  // 如果索引有效，重置状态
  if (index < loadingStates.value.length) {
    loadingStates.value[index] = false;
    errorStates.value[index] = false;
  }
};

// 处理视频错误事件
const handleError = (index: number, _event: Event) => {
  loadingStates.value[index] = false;
  errorStates.value[index] = true;
  errorMessages.value[index] = '视频加载失败，请检查网络连接或视频源';
};

// 处理宫格变化
const handleGridChange = (value: number) => {
  gridCount.value = value;

  // 当宫格数量变化时，调整已选择的数据数量，只保留最后value个
  if (treeValue.value.length > value) {
    // 清理要移除的视频资源
    const removeCount = treeValue.value.length - value;
    for (let i = 0; i < removeCount; i++) {
      cleanupVideoResource(i);
    }

    // 移除多余的数据
    treeValue.value = treeValue.value.slice(-value);
    loadingStates.value = loadingStates.value.slice(-value);
    errorStates.value = errorStates.value.slice(-value);
    errorMessages.value = errorMessages.value.slice(-value);
    flvPlayers.value = flvPlayers.value.slice(-value);
    // 注意：videoRefs不需要切片，因为Vue会自动管理DOM引用
  } else if (treeValue.value.length < value) {
    // 如果宫格数量增加，扩展状态数组
    const addCount = value - treeValue.value.length;
    for (let i = 0; i < addCount; i++) {
      loadingStates.value.push(false);
      errorStates.value.push(false);
      errorMessages.value.push('');
      flvPlayers.value.push(null);
    }
  }
};

// 请求设备列表
const getTreeList = async () => {
  treeLoading.value = true;
  try {
    const res = await fetchRegionAndDevices();
    treeData.value = [res];
  } catch {
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

const onChange = (val: boolean) => {
  if (val) {
    visible.value = true;
    checked.value = false;
    return setTimeout(() => {
      visible.value = false;
    }, 1000);
  }
  checked.value = false;
};

// 组件卸载时清理所有视频资源
onUnmounted(() => {
  // 清除所有视频定时器
  videoTimers.value.forEach((timer) => {
    clearInterval(timer);
  });
  videoTimers.value.clear();
  for (let i = 0; i < flvPlayers.value.length; i++) {
    cleanupVideoResource(i);
  }
});

onMounted(() => {
  getTreeList();
  // 初始化状态数组
  handleGridChange(gridCount.value);
});
</script>

<template>
  <ColPage auto-content-height v-bind="colPageProps">
    <template #left>
      <!-- 设备名称查询 -->
      <div class="flex h-full flex-col">
        <div class="tree-height">
          <Tree
            :checkable="false"
            :data="treeData"
            :tree-loading="treeLoading"
            @select="onSelect"
          />
        </div>
        <div
          class="br-[4px] show-yuntai position-relative h-[300px] bg-[#fff] p-3"
        >
          <div class="position-absolute top-0 flex justify-between">
            <span class="text-yuntai">云台</span>
            <span>
              <Popover v-model:open="visible" trigger="click">
                <template #content>
                  <span>此摄像头不支持云台控制</span>
                </template>
                <Switch v-model:checked="checked" @change="onChange" />
              </Popover>
            </span>
          </div>
          <div class="flex h-[250px] items-center justify-center">
            <img :src="sxt" alt="" />
          </div>
        </div>
      </div>
    </template>
    <div class="ml-3 h-full">
      <template v-if="1">
        <GridView
          v-model="gridCount"
          item-height="min-h-[200px]"
          @grid-change="handleGridChange"
        >
          <template #default="{ index }">
            <div class="flex flex-col items-center justify-center space-y-2">
              <div v-if="treeValue[index]" class="h-full text-lg font-semibold">
                <div
                  class="flexbg-black/50 absolute inset-0 z-10 mr-2 mt-1 cursor-pointer text-right"
                >
                  <span @click="cleanupVideoResource(index)">❌</span>
                </div>
                <video
                  :id="`video-${index}`"
                  :ref="(el) => (videoRefs[index] = el)"
                  autoplay
                  controls
                  muted
                  style="width: 100%; height: 100%; object-fit: fill"
                  @error="handleError(index, $event)"
                ></video>
                <!-- 加载遮罩 -->
                <div
                  v-if="loadingStates[index]"
                  class="absolute inset-0 z-10 flex items-center justify-center bg-black/50"
                >
                  <div class="flex flex-col items-center gap-4">
                    <Spin size="large" />
                    <span class="text-white">视频加载中...</span>
                  </div>
                </div>
                <!-- 错误状态 -->
                <div
                  v-if="errorStates[index]"
                  class="absolute inset-0 z-10 flex items-center justify-center bg-black/50"
                >
                  <div class="flex flex-col items-center gap-4 text-white">
                    <div class="text-4xl">⚠️</div>
                    <div class="text-center">
                      <div class="text-lg font-semibold">视频加载失败</div>
                      <div class="text-sm text-gray-300">
                        {{ errorMessages[index] }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-lg font-semibold text-gray-400">
                <Empty description="请从左侧选择相应设备" />
              </div>
            </div>
          </template>
        </GridView>
      </template>
      <Empty v-else class="mt-[20%]" description="请从左侧选择相应设备" />
    </div>
  </ColPage>
</template>
<style scoped lang="scss">
.tree-height {
  flex: 1;
  overflow: hidden;
}
.show-yuntai {
  border-top: 2px solid #d7d7d7;
}
.text-yuntai {
  color: #b0b0b0;
  font-size: 20px;
  font-weight: 600;
}
</style>
