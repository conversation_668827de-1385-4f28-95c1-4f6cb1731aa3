<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { ColPage } from '@bjy/common-ui';

import EdgeTTSBrowser from '@kingdanx/edge-tts-browser';
import {
  <PERSON><PERSON>,
  Button,
  message,
  Slider,
  Spin,
  Switch,
  Textarea,
  Upload,
} from 'bhidi-design';

import {
  getSettingByCode,
  getSettingById,
  saveAbnormalSettings,
} from '#/api/infoRelease';
import { uploadFile } from '#/api/system/user';
import { fetchRegionAndDevices } from '#/api/videoSurveillance';
import { Tree } from '#/components';

interface TreeNode {
  id: number;
  parentId: string;
  code: string;
  name: string;
  leafNode: boolean;
  children?: TreeNode[];
}

interface SaveData {
  tipsText: string;
  selectSwitch: boolean;
  selectTreeId: any[];
  changeTextArea: string;
  audioUrl: string;
  playSpeed: number;
  soundVolume: number;
}

defineOptions({ name: 'AbnormalSettings' });

// 页面布局配置
const pageProps = reactive({
  leftCollapsible: true,
  leftWidth: 20,
  rightWidth: 80,
  isInternal: true,
  resizable: true,
});

const audioRef = ref<HTMLAudioElement | null>(null);
const treeRef = ref<any>(null);
// 树形数据
const treeData = ref<TreeNode[]>([]);

// 加载状态
const loading = ref(false);
const treeLoading = ref<boolean>(true);
// 存储上次请求的标识符
const lastRequestedIdentifier = ref<null | string>(null);
// 表单数据
const saveData = ref<SaveData>({
  tipsText: '请选择设备',
  selectSwitch: false,
  selectTreeId: [],
  changeTextArea: '',
  audioUrl: '',
  playSpeed: 1,
  soundVolume: 100,
});

// 获取树形列表数据
const getTreeList = async () => {
  treeLoading.value = true;
  try {
    const res = await fetchRegionAndDevices();
    treeData.value = [res];
  } catch {
    treeData.value = [];
  } finally {
    treeLoading.value = false;
  }
};

// 文件上传前处理
const beforeUpload = async (file: File): Promise<boolean> => {
  try {
    loading.value = true;

    // 验证文件类型
    const isMP3 = file.type === 'audio/mpeg' || file.name.endsWith('.mp3');
    if (!isMP3) {
      message.error('上传语音文件必须为MP3格式！');
      return false;
    }

    // 上传文件
    const res = (await uploadFile(file)) as unknown as string;
    saveData.value.audioUrl = res;
    message.success('文件上传成功');
    return true;
  } catch (error) {
    console.error(
      `文件上传失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
    return false;
  } finally {
    loading.value = false;
  }
};

const handleTextToSpeech = async () => {
  // 检查输入文本
  if (!saveData.value.changeTextArea.trim()) {
    message.warning('请输入要转换的文字内容');
    return;
  }

  try {
    loading.value = true;

    // 创建TTS实例
    const tts = new EdgeTTSBrowser();
    tts.tts.setVoiceParams({
      text: saveData.value.changeTextArea,
      voice: 'zh-CN-XiaoxiaoNeural',
      format: 'mp3',
    });

    // 生成音频文件
    const fileName = `output-${tts.tts.fileType.ext}`;
    const blob = await tts.ttsToFile(fileName);
    const file = new File([blob], fileName, { type: 'audio/mpeg' });

    // 上传音频文件
    const res = (await uploadFile(file)) as unknown as string;
    saveData.value.audioUrl = res;
    message.success('语音生成成功');
  } catch (error) {
    console.error(
      `语音生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
    saveData.value.audioUrl = '';
  } finally {
    loading.value = false;
  }
};

const getVoiceReleaseDetail = async (
  identifier: number | string,
  isLeaf: boolean,
) => {
  // const firstNode =
  // saveData.value.selectTreeId?.[saveData.value.selectTreeId.length - 1];
  // const firstLeafNode = firstNode.leafNode;

  // if (!firstNode) return;

  // const currentIdentifier = firstLeafNode ? firstNode.code : firstNode.id;

  // 如果当前标识符与上次相同，则不重复请求
  // if (currentIdentifier === lastRequestedIdentifier.value) {
  //   return;
  // }

  try {
    const res = isLeaf
      ? await getSettingByCode({
          deviceCode: identifier,
        })
      : await getSettingById({
          id: identifier,
        });

    if (res) {
      const {
        voiceContent = '',
        playSpeed = 1,
        soundVolume = 100,
        voiceFile = '',
        status = 0,
      } = Array.isArray(res) && res.length > 0 ? res[0] : res || {};

      saveData.value = {
        ...saveData.value,
        changeTextArea: voiceContent,
        playSpeed: Number(playSpeed) ?? 1,
        soundVolume: Number(soundVolume) ?? 100,
        audioUrl: voiceFile,
        selectSwitch: !!status,
      };

      if (audioRef.value) {
        audioRef.value.volume = Number(soundVolume ?? 100) / 100;
        audioRef.value.playbackRate = Number(playSpeed ?? 1);
      }

      // lastRequestedIdentifier.value = currentIdentifier;
    } else {
      saveData.value = {
        ...saveData.value,
        selectSwitch: false,
        changeTextArea: '',
        audioUrl: '',
        playSpeed: 1,
        soundVolume: 100,
      };
      if (audioRef.value) {
        audioRef.value.volume = 100 / 100;
        audioRef.value.playbackRate = 1;
      }
    }
  } catch (error) {
    console.error('获取发布数据失败:', error);
  }
};

const onSelect = (key: number, node: any) => {
  // 处理节点选中逻辑
  if (Array.isArray(node)) {
    saveData.value.selectTreeId = [...node];
  } else if (node) {
    const index = saveData.value.selectTreeId.findIndex(
      (n) => n.id === node.id,
    );
    if (index === -1) {
      saveData.value.selectTreeId.push(node);
    } else {
      saveData.value.selectTreeId.splice(index, 1);
    }
  }

  // 获取实际选中的节点
  if (treeRef.value) {
    const checkedNodes = treeRef.value.getCheckedNodes();
    saveData.value.selectTreeId = checkedNodes;
  }

  // 更新提示文本
  const selectedNames = saveData.value.selectTreeId
    .filter((item: any) => item.leafNode === true) // 只保留 leafNode 为 true 的项
    .map((item: any) => item.name)
    .filter(Boolean)
    .join('、');

  saveData.value.tipsText = selectedNames
    ? `已选择 ${saveData.value.selectTreeId.filter((item: any) => item.leafNode === true).length} 个设备：${selectedNames}`
    : '请选择设备';

  // 确定要加载配置的目标节点
  let targetNode: TreeNode | undefined;

  if (saveData.value.selectTreeId.length === 0) {
    // 无选中项，重置配置
    saveData.value = {
      tipsText: '请选择设备',
      selectSwitch: false,
      selectTreeId: [],
      changeTextArea: '',
      audioUrl: '',
      playSpeed: 1,
      soundVolume: 100,
    };
    return;
  }

  // 从后往前查找最后一个叶子节点
  for (let i = saveData.value.selectTreeId.length - 1; i >= 0; i--) {
    const currentNode = saveData.value.selectTreeId[i];
    if (currentNode.leafNode === true) {
      targetNode = currentNode;
      break;
    }
  }

  // 如果没有找到叶子节点，则取最后一个选中的节点
  if (!targetNode && saveData.value.selectTreeId.length > 0) {
    targetNode =
      saveData.value.selectTreeId[saveData.value.selectTreeId.length - 1];
  }

  // console.log(targetNode, 'targetNode');
  // 加载目标节点的配置信息
  if (targetNode) {
    const isLeaf = targetNode.leafNode === true;
    const identifier = isLeaf ? targetNode.code : targetNode.id;
    getVoiceReleaseDetail(identifier, isLeaf);
  }
};

const onSave = async () => {
  const {
    selectTreeId,
    changeTextArea,
    selectSwitch,
    audioUrl,
    playSpeed,
    soundVolume,
  } = saveData.value;

  const leafNodes = selectTreeId.filter((item: any) => item.leafNode === true);
  if (leafNodes.length === 0) {
    message.error('请选择设备');
    return;
  }

  try {
    loading.value = true;
    const saveParamsList: any = [];
    leafNodes.map((item) => {
      saveParamsList.push({
        id: item.id,
        deviceCode: item.code,
        voiceContent: changeTextArea,
        playSpeed,
        voiceFile: audioUrl,
        soundVolume,
        status: selectSwitch ? 1 : 0,
        settingType: 1,
      });
    });
    const res = await saveAbnormalSettings(saveParamsList);
    if (res) {
      message.success('保存成功');
      saveData.value = {
        tipsText: '请选择设备',
        selectSwitch: false,
        selectTreeId: [],
        changeTextArea: '',
        audioUrl: '',
        playSpeed: 1,
        soundVolume: 100,
      };
      if (treeRef.value) {
        treeRef.value.clearChecked();
      }
    } else {
      message.error('保存失败');
    }
  } catch (error) {
    console.error(
      `保存失败: ${error instanceof Error ? error.message : '未知错误'}`,
    );
  } finally {
    loading.value = false;
  }
};

const onVolumeChange = () => {
  if (audioRef.value) {
    const currentVolume = audioRef.value.volume;
    saveData.value.soundVolume = Math.round(currentVolume * 100);
  }
};

const onRateChange = () => {
  if (audioRef.value) {
    const currentRate = audioRef.value.playbackRate;
    saveData.value.playSpeed = currentRate;
  }
};

const handleVolumeChange = (value: number) => {
  saveData.value.soundVolume = value;
  if (audioRef.value) {
    audioRef.value.volume = value / 100;
  }
};

onMounted(() => {
  getTreeList();
  if (audioRef.value) {
    audioRef.value.volume = saveData.value.soundVolume / 100;
    audioRef.value.playbackRate = saveData.value.playSpeed;
  }
});
</script>

<template>
  <Spin
    :spinning="loading"
    size="large"
    wrapper-class-name="spin-bg h-full w-full"
  >
    <ColPage auto-content-height v-bind="pageProps">
      <template #left>
        <Tree
          ref="treeRef"
          :check-strictly="false"
          :checkable="true"
          :data="treeData"
          :tree-loading="treeLoading"
          @select="onSelect"
        />
      </template>
      <div class="right-box mb-3 ml-3 overflow-y-auto">
        <div class="mb-[16px]">
          <Alert :message="saveData.tipsText" show-icon type="success" />
        </div>
        <div>
          <div class="flex w-full justify-between">
            <div class="mb-[16px] font-medium">文字显示内容</div>
            <Switch
              v-model:checked="saveData.selectSwitch"
              checked-children="开启"
              un-checked-children="关闭"
            />
          </div>
          <Textarea
            :maxlength="256"
            :rows="4"
            :style="{ resize: 'none' }"
            :value="saveData.changeTextArea"
            @change="(e) => (saveData.changeTextArea = e.target.value ?? '')"
          />
          <div class="mb-[16px] mt-[16px] font-medium">语音播放内容</div>
          <div class="mb-[16px] flex items-center">
            <!-- <div>
              <Upload
                :before-upload="beforeUpload"
                :show-upload-list="false"
                accept=".mp3"
                class="w-full"
              >
                <Button type="primary"> 上传文件 </Button>
              </Upload>
            </div> -->
            <div>
              <Button
                :disabled="!saveData.changeTextArea.trim()"
                ghost
                type="primary"
                @click="handleTextToSpeech"
              >
                文字转语音
              </Button>
            </div>
          </div>
          <div class="mb-[16px]">
            <audio
              ref="audioRef"
              :src="saveData.audioUrl"
              class="h-[35px] w-[300px]"
              controls
              @ratechange="onRateChange"
              @volumechange="onVolumeChange"
            ></audio>
          </div>
          <div class="flex items-center">
            <span class="font-medium">音量：</span>
            <Slider
              id="test"
              v-model:value="saveData.soundVolume"
              class="w-[250px]"
              @change="handleVolumeChange"
            />
            <span class="ml-3 font-medium">{{ saveData.soundVolume }}%</span>
          </div>
        </div>
        <div class="mt-[32px] w-full text-center">
          <!-- :disabled="!saveData.selectTreeId.length || !saveData.audioUrl" -->
          <Button type="primary" @click="onSave"> 保存 </Button>
        </div>
      </div>
    </ColPage>
  </Spin>
</template>

<style scoped lang="less">
.right-box {
  background: #fff;
  height: 100%;
  border-radius: 4px;
  padding: 12px 24px;
}
</style>
