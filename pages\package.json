{"name": "@bjy/pages", "version": "0.0.3", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "pages"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "test:e2e": "playwright test", "test:e2e-ui": "playwright test --ui", "test:e2e-codegen": "playwright codegen"}, "files": ["dist"], "imports": {"#/*": "./src/*"}, "dependencies": {"@bjy-core/menu-ui": "workspace:*", "@bjy/access": "workspace:*", "@bjy/common-ui": "workspace:*", "@bjy/constants": "workspace:*", "@bjy/hooks": "workspace:*", "@bjy/icons": "workspace:*", "@bjy/layouts": "workspace:*", "@bjy/locales": "workspace:*", "@bjy/plugins": "workspace:*", "@bjy/preferences": "workspace:*", "@bjy/request": "workspace:*", "@bjy/stores": "workspace:*", "@bjy/styles": "workspace:*", "@bjy/types": "workspace:*", "@bjy/utils": "workspace:*", "@duyquangnvx/edge-tts": "^3.0.3", "@kingdanx/edge-tts-browser": "^1.0.0", "@tanstack/vue-query": "catalog:", "@vueuse/core": "catalog:", "ant-design-vue": "^4.2.6", "bhidi-design": "catalog:", "dayjs": "catalog:", "edge-tts-browser": "^1.0.1", "flv.js": "^1.6.2", "p-limit": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}}