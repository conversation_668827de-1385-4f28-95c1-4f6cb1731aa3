import { h } from 'vue';

import { SvgConnectIcon, SvgHangUpIcon } from '@bjy/icons';

import { notification } from 'bhidi-design';
// 定义WebSocket消息类型 - 只处理一种通知类型
export interface WebSocketNotificationMessage {
  type: 'notification';
  payload: any;
}

/**
 * 处理来自WebSocket的全局通知消息
 * @param message WebSocket消息 - 只处理一种通知类型
 */
export function handleGlobalNotification(
  message: WebSocketNotificationMessage,
) {
  const key = `notification_${Date.now()}`;
  // 只处理notification类型的消息
  if (message.type === 'notification') {
    const payload = message.payload;

    // 显示全局通知
    if (payload?.title || payload?.message) {
      notification.open({
        message: () =>
          h(
            'div',
            {
              style: {
                fontSize: '16px',
                color: '#fff',
                marginLeft: '32px',
                fontWeight: 600,
              },
            },
            payload.title,
          ),
        description: () =>
          h(
            'div',
            {
              style: {
                fontSize: '14px',
                marginLeft: '32px',
              },
            },
            '紧急呼救',
          ),
        closeIcon: () =>
          h('img', {
            src: '/pages/src/assets/images/user_default.png',
            style: { width: '20px', height: '20px', display: 'none' },
          }),
        duration: 0,
        placement: 'bottomRight',
        style: {
          background: '#d22b2c',
          borderRadius: '8px',
          color: 'white',
          fontWeight: 600,
          height: '100px',
        },
        icon: () =>
          h(
            'div',
            {
              style: {
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                backgroundColor: '#ffb4b6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 'bold',
                fontSize: '18px',
                color: 'red',
              },
            },
            'SOS',
          ),
        btn: () =>
          h(
            'div',
            {
              style: {
                display: 'flex',
                gap: '8px',
              },
            },
            [
              h(SvgHangUpIcon, {
                style: {
                  width: '20px',
                  height: '20px',
                  cursor: 'pointer',
                  fill: '#4caf50',
                },
                onClick: () => notification.close(key),
              }),
              h(SvgConnectIcon, {
                style: {
                  width: '20px',
                  height: '20px',
                  cursor: 'pointer',
                  fill: '#4caf50',
                },
                onClick: () => notification.close(key),
              }),
            ],
          ),
        key,
        onClose: close,
      });
    }
  }
}
