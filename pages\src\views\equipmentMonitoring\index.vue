<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import {
  Page,
  type VbenFormProps,
  useVbenModal,
  ColPage,
} from '@bjy/common-ui';
import {
  Switch,
  message,
  TimePicker,
  Button,
  Popconfirm,
  Dropdown,
  Menu,
} from 'bhidi-design';
import dayjs from 'dayjs';
import { useVbenVxeGrid, type VxeGridProps } from '#/adapter/vxe-table';
import {
  getPoleDevicePage,
  getRegionAndDevice,
  putPoleDeviceUpdate,
} from '#/api/equipmentMonitoring/Monitoring';
import { columns, searchFormSchema } from './data';
import { Tree } from '#/components';
// import { useEquipmentMonitoringStore } from '#/store/equipmentMonitoring';
// const store = useEquipmentMonitoringStore();
defineOptions({ name: 'EquipmentMonitoring' });

// 时间选择相关状态
const selectedTimeStart = ref<any>(null);
const selectedTimeEnd = ref<any>(null);

// 控制类型和弹窗状态
const currentControlType = ref<'speaker' | 'lamp' | 'LED'>('speaker');
const currentModalTitle = ref('设置喇叭运行时间');
const currentDeviceForControl = ref<any>(null);
const isBatchMode = ref(false);
const selectedDevices = ref<any[]>([]);

// 用于控制开关状态的临时变量，防止点击时立即切换
const pendingToggleDevice = ref<any>(null);
const pendingToggleType = ref<string>('');

// 获取开关的实际显示状态
const getSwitchState = (row: any, controlType: 'speaker' | 'lamp' | 'LED') => {
  const controlField =
    controlType === 'speaker'
      ? 'hornState'
      : controlType === 'lamp'
        ? 'rblState'
        : 'ledState';

  // 如果这个设备正在待处理状态，且是对应的控制类型，则保持关闭状态
  if (
    pendingToggleDevice.value === row &&
    pendingToggleType.value === controlField
  ) {
    return false;
  }

  // 返回布尔值：1表示开启，0表示关闭
  return row[controlField] === 1;
};

// 表格勾选状态
const selectedRows = ref<any[]>([]);

// 时间选择弹窗
const [TimeModal, timeModalApi] = useVbenModal({
  onOpenChange: async (open) => {
    if (!open) {
      // 弹窗关闭时清空数据
      selectedTimeStart.value = null;
      selectedTimeEnd.value = null;
      timeModalApi.setData({});
      currentDeviceForControl.value = null;
      isBatchMode.value = false;
      selectedDevices.value = [];
    } else {
      // 弹窗打开时回显数据
      loadTimeData();
    }
  },
});

// 加载时间数据（回显功能）
const loadTimeData = () => {
  if (isBatchMode.value) {
    // 批量模式：检查选中设备是否有相同的时间设置
    const devices = selectedDevices.value;
    if (devices.length > 0) {
      const controlType = currentControlType.value;
      const startTimeField =
        controlType === 'speaker'
          ? 'hornStartTime'
          : controlType === 'lamp'
            ? 'rblStartTime'
            : 'ledStartTime';
      const endTimeField =
        controlType === 'speaker'
          ? 'hornEndTime'
          : controlType === 'lamp'
            ? 'rblEndTime'
            : 'ledEndTime';

      // 获取第一个设备的时间作为参考
      const firstDevice = devices[0];
      const startTime = firstDevice[startTimeField];
      const endTime = firstDevice[endTimeField];

      // 检查所有设备是否有相同的时间设置
      const hasSameTime = devices.every(
        (device) =>
          device[startTimeField] === startTime &&
          device[endTimeField] === endTime,
      );

      if (hasSameTime && startTime && endTime) {
        // 如果所有设备时间一致且有值，则回显
        selectedTimeStart.value = dayjs(startTime, 'HH:mm:ss');
        selectedTimeEnd.value = dayjs(endTime, 'HH:mm:ss');
      } else {
        // 时间不一致或为空，清空选择
        selectedTimeStart.value = null;
        selectedTimeEnd.value = null;
      }
    }
  } else {
    // 单设备模式：回显当前设备的时间设置
    const device = currentDeviceForControl.value;
    if (device) {
      const controlType = currentControlType.value;
      const startTimeField =
        controlType === 'speaker'
          ? 'hornStartTime'
          : controlType === 'lamp'
            ? 'rblStartTime'
            : 'ledStartTime';
      const endTimeField =
        controlType === 'speaker'
          ? 'hornEndTime'
          : controlType === 'lamp'
            ? 'rblEndTime'
            : 'ledEndTime';

      const startTime = device[startTimeField];
      const endTime = device[endTimeField];

      if (startTime && endTime) {
        selectedTimeStart.value = dayjs(startTime, 'HH:mm:ss');
        selectedTimeEnd.value = dayjs(endTime, 'HH:mm:ss');
      } else {
        selectedTimeStart.value = null;
        selectedTimeEnd.value = null;
      }
    }
  }
};

// 查询条件
const formOptions: VbenFormProps = {
  schema: searchFormSchema,
  showCollapseButton: false,
};

const gridOptions: VxeGridProps<any> = {
  columns,
  pagerConfig: {
    pageSize: 10,
  },
  columnConfig: {
    resizable: false,
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: false,
    zoom: false,
  },
  stripe: true,
  height: 'auto',
  align: 'left',
  border: 'inner',
  checkboxConfig: {
    reserve: true,
    highlight: true,
    range: true,
    checkMethod: ({ row }: { row: any }) => {
      // 只有在线设备才能被勾选 (linkState: 2表示在线)
      return row.linkState === 2 || row.linkState === '在线';
    },
  },
  proxyConfig: {
    immediate: false,
    ajax: {
      query: async ({ page }, formValues) => {
        // 如果不允许查询或没有选中树节点，不执行查询
        if (!allowQuery.value || !selectedTreeNode.value) {
          return { items: [], total: 0 };
        }
        // 使用选中的树节点数据
        const treeParams = {
          district: selectedTreeNode.value.id || selectedTreeNode.value.code,
        };
        const data = await getPoleDevicePage({
          pageNo: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          ...treeParams,
        });
        return { ...data, items: data.list };
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: (e: Record<string, any>) => {
      selectedRows.value = e.records;
    },
    checkboxAll: (e: Record<string, any>) => {
      selectedRows.value = e.records;
    },
  },
});

// 通用控制开关处理
const handleControlToggle = (
  row: any,
  value: boolean,
  controlType: 'speaker' | 'lamp' | 'LED',
) => {
  if (value) {
    // 用户想要开启控制 - 显示时间选择弹窗
    const controlField =
      controlType === 'speaker'
        ? 'hornState'
        : controlType === 'lamp'
          ? 'rblState'
          : 'ledState';
    const wasAlreadyOn = row[controlField] === 1;

    // 如果原本是关闭的，设置为待处理状态，阻止开关切换
    if (!wasAlreadyOn) {
      pendingToggleDevice.value = row;
      pendingToggleType.value = controlField;
    }

    currentControlType.value = controlType;
    currentDeviceForControl.value = row;
    isBatchMode.value = false;

    // 设置动态标题
    const titleMap = {
      speaker: wasAlreadyOn ? '编辑喇叭运行时间' : '设置喇叭运行时间',
      lamp: wasAlreadyOn ? '编辑红蓝灯运行时间' : '设置红蓝灯运行时间',
      LED: wasAlreadyOn ? '编辑LED运行时间' : '设置LED运行时间',
    };
    currentModalTitle.value = titleMap[controlType];

    timeModalApi.setData({ row, deviceId: row.id, controlType, wasAlreadyOn });
    timeModalApi.open();
  }
};

// 雷达开关切换处理
const handleRadarToggle = (row: any, value: boolean) => {
  handleControlToggle(row, value, 'speaker');
};

// 红蓝灯开关切换处理
const handleLampToggle = (row: any, value: boolean) => {
  handleControlToggle(row, value, 'lamp');
};

// LED开关切换处理
const handleLEDToggle = (row: any, value: boolean) => {
  handleControlToggle(row, value, 'LED');
};

// 通用的关闭控制确认
const handleControlClose = async (
  row: any,
  controlType: 'speaker' | 'lamp' | 'LED',
) => {
  const controlField =
    controlType === 'speaker'
      ? 'hornState'
      : controlType === 'lamp'
        ? 'rblState'
        : 'ledState';
  const startTimeField =
    controlType === 'speaker'
      ? 'hornStartTime'
      : controlType === 'lamp'
        ? 'rblStartTime'
        : 'ledStartTime';
  const endTimeField =
    controlType === 'speaker'
      ? 'hornEndTime'
      : controlType === 'lamp'
        ? 'rblEndTime'
        : 'ledEndTime';

  const nameMap = {
    speaker: '喇叭',
    lamp: '红蓝灯',
    LED: 'LED',
  };

  try {
    message.loading(`正在关闭${nameMap[controlType]}...`, 0);

    // 准备更新数据数组（单个设备也放入数组）
    const updateDataArray = [
      {
        ...row,
        [startTimeField]: '',
        [endTimeField]: '',
        [controlField]: 0,
      },
    ];

    // 调用API更新设备（传递数组）
    await putPoleDeviceUpdate(updateDataArray);

    // 更新本地数据
    row[startTimeField] = '';
    row[endTimeField] = '';
    row[controlField] = 0;

    message.destroy();
    message.success(`${nameMap[controlType]}已关闭`);
  } catch (error: any) {
    message.destroy();
    message.error(`关闭失败：${error.message || '未知错误'}`);
  }
};

// 取消关闭控制
const handleControlCloseCancel = (
  row: any,
  controlType: 'speaker' | 'lamp' | 'LED',
) => {
  // 保持控制开启状态
};

// 批量控制函数 - 开启
const handleBatchControl = (controlType: 'speaker' | 'lamp' | 'LED') => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要控制的设备');
    return;
  }

  // 由于checkbox已经限制只能选择在线设备，这里直接使用选中的设备
  selectedDevices.value = selectedRows.value;
  currentControlType.value = controlType;
  isBatchMode.value = true;

  // 清空之前的时间选择
  selectedTimeStart.value = null;
  selectedTimeEnd.value = null;

  // 设置动态标题
  const titleMap = {
    speaker: '批量设置喇叭运行时间',
    lamp: '批量设置红蓝灯运行时间',
    LED: '批量设置LED运行时间',
  };
  currentModalTitle.value = titleMap[controlType];

  timeModalApi.setData({
    controlType,
    isBatch: true,
    devices: selectedRows.value,
  });
  timeModalApi.open();
};

// 批量关闭控制函数
const handleBatchControlClose = async (
  controlType: 'speaker' | 'lamp' | 'LED',
) => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要控制的设备');
    return;
  }

  const controlField =
    controlType === 'speaker'
      ? 'hornState'
      : controlType === 'lamp'
        ? 'rblState'
        : 'ledState';
  const startTimeField =
    controlType === 'speaker'
      ? 'hornStartTime'
      : controlType === 'lamp'
        ? 'rblStartTime'
        : 'ledStartTime';
  const endTimeField =
    controlType === 'speaker'
      ? 'hornEndTime'
      : controlType === 'lamp'
        ? 'rblEndTime'
        : 'ledEndTime';

  const nameMap = {
    speaker: '喇叭',
    lamp: '红蓝灯',
    LED: 'LED',
  };

  try {
    message.loading(`正在批量关闭${nameMap[controlType]}...`, 0);

    // 准备批量更新数据数组
    const updateDataArray = selectedRows.value.map((device) => ({
      ...device,
      [startTimeField]: '',
      [endTimeField]: '',
      [controlField]: 0,
    }));

    // 批量关闭操作 - 传递数组数据
    await putPoleDeviceUpdate(updateDataArray);

    // 更新本地数据
    selectedRows.value.forEach((device) => {
      device[startTimeField] = '';
      device[endTimeField] = '';
      device[controlField] = 0;
    });

    const deviceCount = selectedRows.value.length;

    // 清除表格勾选状态
    selectedRows.value = [];
    gridApi.grid.clearCheckboxRow();

    message.destroy();
    message.success(
      `批量关闭${nameMap[controlType]}成功，已关闭${deviceCount}个设备的${nameMap[controlType]}`,
    );
  } catch (error: any) {
    message.destroy();
    message.error(`批量关闭失败：${error.message || '未知错误'}`);
  }
};

const handleTimeConfirm = async () => {
  // 基本验证时间是否已选择
  if (!selectedTimeStart.value || !selectedTimeEnd.value) {
    message.error('请选择完整的时间段');
    return;
  }

  // 转换为时间字符串进行比较
  const startTimeStr = selectedTimeStart.value.format('HH:mm:ss');
  const endTimeStr = selectedTimeEnd.value.format('HH:mm:ss');

  // 验证时间逻辑
  if (startTimeStr >= endTimeStr) {
    message.error('开始时间必须早于结束时间');
    return;
  }

  const controlType = currentControlType.value;

  try {
    const controlName = {
      speaker: '喇叭',
      lamp: '红蓝灯',
      LED: 'LED',
    }[controlType];

    const operationType = isBatchMode.value ? '批量设置' : '设置';
    message.loading(`正在${operationType}${controlName}运行时间...`, 0);

    if (isBatchMode.value) {
      // 批量操作 - 准备数组数据
      const controlField =
        controlType === 'speaker'
          ? 'hornState'
          : controlType === 'lamp'
            ? 'rblState'
            : 'ledState';
      const startTimeField =
        controlType === 'speaker'
          ? 'hornStartTime'
          : controlType === 'lamp'
            ? 'rblStartTime'
            : 'ledStartTime';
      const endTimeField =
        controlType === 'speaker'
          ? 'hornEndTime'
          : controlType === 'lamp'
            ? 'rblEndTime'
            : 'ledEndTime';

      const updateDataArray = selectedDevices.value.map((device) => ({
        ...device,
        [startTimeField]: startTimeStr,
        [endTimeField]: endTimeStr,
        [controlField]: 1,
      }));

      // 批量操作 - 传递数组数据
      await putPoleDeviceUpdate(updateDataArray);

      // 更新本地数据
      selectedDevices.value.forEach((device) => {
        device[startTimeField] = startTimeStr;
        device[endTimeField] = endTimeStr;
        device[controlField] = 1;
      });

      // 清除表格勾选状态
      selectedRows.value = [];
      gridApi.grid.clearCheckboxRow();

      message.destroy();
      message.success(
        `批量${controlName}时间设置成功，已开启${selectedDevices.value.length}个设备的${controlName}`,
      );
    } else {
      // 单个设备操作
      const device = currentDeviceForControl.value;
      if (device) {
        const controlField =
          controlType === 'speaker'
            ? 'hornState'
            : controlType === 'lamp'
              ? 'rblState'
              : 'ledState';
        const startTimeField =
          controlType === 'speaker'
            ? 'hornStartTime'
            : controlType === 'lamp'
              ? 'rblStartTime'
              : 'ledStartTime';
        const endTimeField =
          controlType === 'speaker'
            ? 'hornEndTime'
            : controlType === 'lamp'
              ? 'rblEndTime'
              : 'ledEndTime';

        // 准备更新数据数组（单个设备也放入数组）
        const updateDataArray = [
          {
            ...device,
            [startTimeField]: startTimeStr,
            [endTimeField]: endTimeStr,
            [controlField]: 1,
          },
        ];

        await putPoleDeviceUpdate(updateDataArray);

        // 更新本地数据
        device[startTimeField] = startTimeStr;
        device[endTimeField] = endTimeStr;
        device[controlField] = 1;

        message.destroy();
        message.success(`${controlName}时间设置成功，${controlName}已开启`);
      }
    }

    // 清除待处理状态
    pendingToggleDevice.value = null;
    pendingToggleType.value = '';

    timeModalApi.close();
    selectedTimeStart.value = null;
    selectedTimeEnd.value = null;
  } catch (error: any) {
    message.destroy();
    message.error(`设置失败：${error.message || '未知错误'}`);
  }
};

// 取消时间选择
const handleTimeCancel = () => {
  // 清除待处理状态
  pendingToggleDevice.value = null;
  pendingToggleType.value = '';

  timeModalApi.close();
  selectedTimeStart.value = null;
  selectedTimeEnd.value = null;
};

const treeData = ref([]);
const selectedTreeNode = ref<any>(null); // 存储选中的树节点
const allowQuery = ref(false); // 控制是否允许查询
const treeLoading = ref(false);

// 定时刷新相关
const refreshTimer = ref<NodeJS.Timeout | null>(null);

const getRegionAndDeviceFn = async () => {
  try {
    treeLoading.value = true;
    const res = await getRegionAndDevice();
    // 递归函数，限制树的层级到4层
    const limitTreeLevels = (node: any, currentLevel: number = 1): any => {
      if (currentLevel >= 3) {
        // 到达第4层时，移除children属性
        const { children, ...nodeWithoutChildren } = node;
        return nodeWithoutChildren;
      }

      if (node.children && Array.isArray(node.children)) {
        return {
          ...node,
          children: node.children.map((child: any) =>
            limitTreeLevels(child, currentLevel + 1),
          ),
        };
      }

      return node;
    };

    treeData.value = [limitTreeLevels(res)];
    treeLoading.value = false;
  } catch {
    treeData.value = [];
    treeLoading.value = false;
  }
};
onMounted(() => {
  getRegionAndDeviceFn();
});

// 组件销毁时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});

// 启动定时刷新
const startAutoRefresh = () => {
  // 清除之前的定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
  }

  // 设置5分钟定时刷新
  refreshTimer.value = setInterval(
    () => {
      gridApi.query();
    },
    5 * 60 * 1000,
  ); // 5分钟 = 5 * 60 * 1000毫秒
};

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
};

const onSelect = (key: any, node: any) => {
  selectedTreeNode.value = node; // 保存选中的节点
  allowQuery.value = true; // 允许查询
  gridApi.reload(); // 不需要传参数，直接重新加载

  // 启动定时刷新
  startAutoRefresh();
};

// 格式化设备位置显示
const formatDevicePosition = (position: any): string => {
  if (!position || typeof position !== 'string') {
    return 'N/A,N/A,N/A';
  }

  const parts = position.split(',').map((part) => part.trim());

  const longitude = parts[0] || 'N/A';
  const latitude = parts[1] || 'N/A';
  const altitude = parts[2] || 'N/A';

  return `${longitude}°,${latitude}°,${altitude}m`;
};

const props = reactive({
  leftCollapsible: true,
  leftWidth: 20,
  rightWidth: 80,
  isInternal: true,
  resizable: true,
});
</script>

<template>
  <ColPage auto-content-height v-bind="props">
    <template #left>
      <Tree
        :checkable="false"
        :data="treeData"
        @select="onSelect"
        :treeLoading="treeLoading"
        :isIcon="false"
      />
    </template>
    <div class="right-box mb-3 ml-3 flex flex-col">
      <Grid>
        <template #toolbar-actions>
          <div>
            <span style="font-size: 16px; font-weight: bold">设备列表</span>
          </div>
        </template>
        <template #toolbar-tools>
          <div class="flex gap-2">
            <!-- 批量喇叭控制下拉菜单 -->
            <Dropdown :disabled="selectedRows.length === 0">
              <Button type="default" class="flex items-center">
                批量喇叭控制
                <svg
                  class="ml-1 h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </Button>
              <template #overlay>
                <Menu>
                  <Menu.Item
                    key="speaker-on"
                    @click="() => handleBatchControl('speaker')"
                  >
                    批量开启喇叭
                  </Menu.Item>
                  <Menu.Item
                    key="speaker-off"
                    @click="() => handleBatchControlClose('speaker')"
                  >
                    批量关闭喇叭
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>

            <!-- 批量红蓝灯控制下拉菜单 -->
            <Dropdown :disabled="selectedRows.length === 0">
              <Button type="default" class="flex items-center">
                批量红蓝灯控制
                <svg
                  class="ml-1 h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </Button>
              <template #overlay>
                <Menu>
                  <Menu.Item
                    key="lamp-on"
                    @click="() => handleBatchControl('lamp')"
                  >
                    批量开启红蓝灯
                  </Menu.Item>
                  <Menu.Item
                    key="lamp-off"
                    @click="() => handleBatchControlClose('lamp')"
                  >
                    批量关闭红蓝灯
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>

            <!-- 批量LED控制下拉菜单 -->
            <Dropdown :disabled="selectedRows.length === 0">
              <Button type="default" class="flex items-center">
                批量LED控制
                <svg
                  class="ml-1 h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </Button>
              <template #overlay>
                <Menu>
                  <Menu.Item
                    key="led-on"
                    @click="() => handleBatchControl('LED')"
                  >
                    批量开启LED
                  </Menu.Item>
                  <Menu.Item
                    key="led-off"
                    @click="() => handleBatchControlClose('LED')"
                  >
                    批量关闭LED
                  </Menu.Item>
                </Menu>
              </template>
            </Dropdown>
            <Button type="primary"> 刷新 </Button>
          </div></template
        >
        <!-- 是否在线插槽 -->
        <template #linkState="{ row }">
          <span
            :class="{
              'text-red-500': row.linkState === 1 || row.linkState === '离线',
              'text-green-500': row.linkState === 2 || row.linkState === '在线',
            }"
          >
            {{
              row.linkState === 2 || row.linkState === '在线' ? '在线' : '离线'
            }}
          </span>
        </template>

        <!-- 电量插槽 -->
        <template #powerLevel="{ row }">
          <span
            :class="{
              'text-red-500': parseInt(row.powerLevel.replace('%', '')) < 30,
            }"
          >
            {{ row.powerLevel }}
          </span>
        </template>

        <!-- 信号插槽 -->
        <template #deviceSignal="{ row }">
          <span
            :class="{
              'text-red-500': parseInt(row.deviceSignal.replace('%', '')) < 30,
            }"
          >
            {{ row.deviceSignal }}
          </span>
        </template>

        <!-- 位置 (经度°,维度°,高度m)插槽 -->
        <template #devicePosition="{ row }">
          <span>{{ formatDevicePosition(row.devicePosition) }}</span>
        </template>

        <!-- 雷达控制插槽 -->
        <template #hornState="{ row }">
          <div class="flex items-center justify-center" @click.stop>
            <Popconfirm
              v-if="getSwitchState(row, 'speaker')"
              title="确定要关闭喇叭吗？"
              description="关闭后将清除已设置的运行时间"
              ok-text="确定"
              cancel-text="取消"
              @confirm="() => handleControlClose(row, 'speaker')"
              @cancel="() => handleControlCloseCancel(row, 'speaker')"
            >
              <Switch :checked="getSwitchState(row, 'speaker')" size="small" />
            </Popconfirm>
            <Switch
              v-else
              :checked="getSwitchState(row, 'speaker')"
              size="small"
              @click="
                () => handleRadarToggle(row, !getSwitchState(row, 'speaker'))
              "
            />
          </div>
        </template>

        <!-- 红蓝灯控制插槽 -->
        <template #rblState="{ row }">
          <div class="flex items-center justify-center" @click.stop>
            <Popconfirm
              v-if="getSwitchState(row, 'lamp')"
              title="确定要关闭红蓝灯吗？"
              description="关闭后将清除已设置的运行时间"
              ok-text="确定"
              cancel-text="取消"
              @confirm="() => handleControlClose(row, 'lamp')"
              @cancel="() => handleControlCloseCancel(row, 'lamp')"
            >
              <Switch :checked="getSwitchState(row, 'lamp')" size="small" />
            </Popconfirm>
            <Switch
              v-else
              :checked="getSwitchState(row, 'lamp')"
              size="small"
              @click="() => handleLampToggle(row, !getSwitchState(row, 'lamp'))"
            />
          </div>
        </template>

        <!-- LED控制插槽 -->
        <template #ledState="{ row }">
          <div class="flex items-center justify-center" @click.stop>
            <Popconfirm
              v-if="getSwitchState(row, 'LED')"
              title="确定要关闭LED吗？"
              description="关闭后将清除已设置的运行时间"
              ok-text="确定"
              cancel-text="取消"
              @confirm="() => handleControlClose(row, 'LED')"
              @cancel="() => handleControlCloseCancel(row, 'LED')"
            >
              <Switch :checked="getSwitchState(row, 'LED')" size="small" />
            </Popconfirm>
            <Switch
              v-else
              :checked="getSwitchState(row, 'LED')"
              size="small"
              @click="() => handleLEDToggle(row, !getSwitchState(row, 'LED'))"
            />
          </div>
        </template>
      </Grid>
    </div>

    <!-- 时间选择弹窗 -->
    <TimeModal
      :title="currentModalTitle"
      :width="600"
      @cancel="handleTimeCancel"
    >
      <div class="p-4">
        <div class="mb-4">
          <label class="mb-2 block text-sm font-medium">
            选择运行时间段（起止：时:分:秒）：<span class="text-red-500"
              >*</span
            >
          </label>
          <div class="flex items-center gap-2">
            <TimePicker
              v-model:value="selectedTimeStart"
              format="HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-full"
              :allowClear="false"
              @change="
                (time: any) => {
                  selectedTimeStart = time;
                }
              "
            />
            <span>~</span>
            <TimePicker
              v-model:value="selectedTimeEnd"
              format="HH:mm:ss"
              placeholder="请选择结束时间"
              class="w-full"
              :allowClear="false"
              @change="
                (time: any) => {
                  selectedTimeEnd = time;
                }
              "
            />
          </div>
          <div class="mt-2 text-xs text-gray-500">
            注意：开始时间必须早于结束时间，且运行时间至少需要30分钟
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <Button @click="handleTimeCancel"> 取消 </Button>
          <Button type="primary" @click="handleTimeConfirm"> 确定 </Button>
        </div>
      </template>
    </TimeModal>
  </ColPage>
</template>
<style scoped lang="less">
:deep(.vxe-buttons--wrapper) {
  display: black;
}
.right-box {
  background: #fff;
  height: 100%;
  border-radius: 4px;
  // padding: 12px 24px;

  .right-fixed-box {
    flex: 1;
  }

  .right-scroll {
    height: calc(100% - 60px);
  }

  .right-scroll-box {
    flex: 1;
  }
}
:deep(.search-table-gap) {
  background: rgba(245, 246, 248) ;
}
:deep(.vxe-grid--layout-body-wrapper) {
  padding: 0 24px;
}
</style>
