import type { VxeGridProps } from '@bjy/plugins/vxe-table';
import type { VbenFormSchema } from '#/adapter/form';
import { z } from '#/adapter/form';

export const equipmentColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  { title: '序号', type: 'seq', width: 60 },

  {
    field: 'eventName',
    title: '事件名称',
    minWidth: 100,
    slots: { default: 'eventName' },
  },
  {
    field: 'monitorDevice',
    title: '监控设备',
    minWidth: 100,
  },
  {
    field: 'happenTime',
    title: '发生时间',
    formatter: 'formatDateTime',
    minWidth: 165,
  },
  {
    field: 'eventCurator',
    title: '负责人',
    minWidth: 80,
  },
  {
    field: 'phone',
    title: '联系电话',
    minWidth: 120,
  },
  {
    field: 'email',
    title: '联系邮箱',
    minWidth: 185,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    editRender: { name: 'input' },
  },
  {
    field: 'status',
    title: '状态',
    minWidth: 120,
    slots: { default: 'status' },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: '130px',
  },
];

export const searchFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入事件名称',
    },
    fieldName: 'eventName',
    label: '事件名称',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入监控设备',
    },
    fieldName: 'monitorDevice',
    label: '监控设备',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入负责人',
    },
    fieldName: 'eventCurator',
    label: '负责人',
  },
  {
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['请选择开始时间', '请选择结束时间'],
      showTime: true,
    },
    fieldName: 'happenTime',
    label: '发生时间',
  },
];

// equipment　处置 - 发送邮件
export const equipmentEmailFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 100,
      placeholder: '请输入邮箱地址',
    },
    fieldName: 'email',
    label: '邮箱',
    rules: z
      .string()
      .min(1, { message: '请输入邮箱地址' })
      .email('请输入正确的邮箱格式'),
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 100,
      placeholder: '请输入邮件主题',
    },
    fieldName: 'subject',
    label: '主题',
    rules: 'required',
  },
  {
    component: 'Textarea',
    componentProps: {
      autocomplete: 'off',
      maxlength: 256,
      placeholder: '请输入邮件内容...',
      rows: 4,
    },
    fieldName: 'content',
    label: '邮件内容',
    rules: 'required',
  },
];

// equipment　处置 - 发送短信
export const equipmentSmsFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 11,
      placeholder: '请输入手机号码',
    },
    fieldName: 'phone',
    label: '手机号',
    rules: z
      .string()
      .min(1, { message: '请输入手机号码' })
      .refine(
        (value) => {
          const phoneRegex = /^1[3-9]\d{9}$/;
          return phoneRegex.test(value);
        },
        {
          message: '请输入正确的手机号码格式',
        },
      ),
  },
  {
    component: 'Textarea',
    componentProps: {
      autocomplete: 'off',
      maxlength: 256,
      placeholder: '请输入短信内容...',
      rows: 4,
    },
    fieldName: 'content',
    label: '短信内容',
    rules: 'required',
  },
];

// equipment　业务事件详情
export const equipmentEventDetailsFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入事件名称',
      readonly: true,
      disabled: true,
    },
    fieldName: 'eventName',
    label: '事件名称',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入监控设备',
      readonly: true,
      disabled: true,
    },
    fieldName: 'monitorDevice',
    label: '监控设备',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入发生时间',
      readonly: true,
      disabled: true,
    },
    fieldName: 'happenTime',
    label: '发生时间',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入负责人',
    },
    fieldName: 'eventCurator',
    label: '负责人',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入联系方式',
    },
    fieldName: 'phone',
    label: '联系电话',
    rules: z
      .string()
      .min(1, { message: '请输入手机号码' })
      .refine(
        (value) => {
          const phoneRegex = /^1[3-9]\d{9}$/;
          return phoneRegex.test(value);
        },
        {
          message: '请输入正确的手机号码格式',
        },
      ),
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入联系方式',
    },
    fieldName: 'email',
    label: '联系邮箱',
    rules: z
      .string()
      .min(1, { message: '请输入邮箱' })
      .refine(
        (value) => {
          const emailRegex =
            /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z\u00A1-\uFFFF]{2,63}$/;
          return emailRegex.test(value);
        },
        {
          message: '请输入正确的邮箱格式',
        },
      ),
  },

  {
    component: 'Textarea',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入备注',
    },
    fieldName: 'remark',
    label: '备注',
  },

  {
    component: 'Input',
    fieldName: 'eventType',
    label: '',
    dependencies: {
      show: false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'id',
    label: '',
    dependencies: {
      show: false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    fieldName: 'status',
    label: '',
    dependencies: {
      show: false,
      triggerFields: [''],
    },
  },
];
