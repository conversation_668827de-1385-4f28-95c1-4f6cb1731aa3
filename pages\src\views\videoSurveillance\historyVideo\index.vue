<script lang="ts" setup>
import type { TreeProps } from 'bhidi-design';

import { onMounted, reactive, ref } from 'vue';

import { ColPage } from '@bjy/common-ui';
import { IconifyIcon } from '@bjy/icons';

import { Button, DatePicker, Empty, message, Table } from 'bhidi-design';

import {
  fetchHistoryVideo,
  fetchRegionAndDevices,
} from '#/api/videoSurveillance';

import GridView from '../../../components/GridView/index.vue';
import TreeSelect from '../components/TreeSelect/index.vue';
import VideoPlayer from '../components/VideoPlayer/index.vue';
import { tableColumns } from './data';

defineOptions({ name: 'HistoryVideo' });

const colPageProps = reactive({
  leftCollapsible: true,
  leftWidth: 20,
  rightWidth: 80,
  isInternal: true,
  resizable: true,
  // 优化收起后的布局
  collapsedWidth: 0,
  expandWidth: 20,
});

const treeList = ref<any[]>([]);
// 这是treeSelect的选中值，只用来做查询参数
const treeValue = ref<any[]>([]);

// 这是表格点击行选中的值，可用于渲染右侧宫格播放
const deviceItems = ref<any[]>([]);

const selectedDevices = ref<any[]>([]);
const deviceList = ref<any[]>([]);

const fieldNames: TreeProps['fieldNames'] = {
  children: 'children',
  label: 'name',
  value: 'id',
};

const gridCount = ref<number>(1);

// 日期选择相关状态
const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const selectedDate = ref<string>(getCurrentDate());
const loading = ref<boolean>(false);
const deviceLoading = ref<boolean>(false);

const handleSelect = (_keys: any[], _nodes: any[]) => {
  deviceItems.value = [];
  selectedDevices.value = _nodes;
};

// 处理节点不可选择事件
const handleNodeNotSelectable = (data: any) => {
  const { node } = data;

  if (typeof node.leafNode === 'boolean') {
    !node.leafNode && message.info('只能选择具体的设备');
  } else {
    message.info('该项不可选择');
  }
};

// 处理宫格变化
const handleGridChange = (value: number) => {
  gridCount.value = value;

  // 当宫格数量变化时，调整已选择的数据数量，只保留最后value个
  if (deviceItems.value.length > value) {
    // 如果当前选择的数量超过新的宫格数，只保留最后value个
    deviceItems.value = deviceItems.value.slice(-value);
  }
};

// 查询视频列表
const searchVideos = async () => {
  try {
    if (selectedDevices.value.length === 0) {
      message.info('请选择具体的设备');
      return;
    }
    loading.value = true;
    const res = await fetchHistoryVideo({
      deviceCode: selectedDevices.value[0]?.code || '',
      queryTime: selectedDate.value || '',
    });
    if (res) {
      // 为数据添加index字段
      const dataWithIndex =
        res?.map((item: any, index: number) => ({
          ...item,
          index: index + 1,
        })) || [];

      deviceList.value = dataWithIndex;

      // 如果有数据，自动选中第一个视频播放
      if (dataWithIndex.length > 0) {
        // 清空当前选中的视频
        deviceItems.value = [];
        // 选中第一个视频
        deviceItems.value.push(dataWithIndex[0]);
      }
    } else {
      console.error('查询失败: 接口返回数据格式错误');
      deviceList.value = [];
      // 清空选中的视频
      deviceItems.value = [];
    }
  } catch (error) {
    console.error('接口调用失败:', error);
    deviceList.value = [];
    // 清空选中的视频
    deviceItems.value = [];
  } finally {
    loading.value = false;
  }
};

// 处理日期变化
const handleDateChange = (_value: any | string, dateString: string) => {
  selectedDate.value = dateString;
};

// 请求接口
const getTreeList = async () => {
  try {
    deviceLoading.value = true;
    const res = await fetchRegionAndDevices();
    treeList.value = res ? [res] : [];
  } catch (error) {
    console.error('获取设备列表失败:', error);
    treeList.value = [];
  } finally {
    deviceLoading.value = false;
  }
};

// 视频播放器事件处理
const handleVideoLoaded = () => {
  // 视频加载完成
};

const handleVideoError = (error: Error) => {
  console.error('视频加载错误:', error);
};

const handleDownloadComplete = (_filename: string) => {
  // 视频下载完成
};

// 移除视频
const removeVideo = (index: number) => {
  if (index >= 0 && index < deviceItems.value.length) {
    deviceItems.value.splice(index, 1);
  }
};

// 处理表格行点击
const handleRowClick = (record: any) => {
  const index = deviceItems.value.findIndex((item) => item.id === record.id);

  if (index === -1) {
    // 如果未选中，则添加到选中列表
    if (deviceItems.value.length >= gridCount.value) {
      // 如果已达到宫格数限制，从头部删除一个，再从尾部追加
      deviceItems.value.shift();
      deviceItems.value.push(record);
    } else {
      // 如果未达到宫格数限制，直接添加
      deviceItems.value.push(record);
    }
  } else {
    // 如果已选中，则取消选中
    deviceItems.value.splice(index, 1);
  }
};

const handleCustomRow = (record: any) => {
  const isSelected = deviceItems.value.some((item) => item.id === record.id);

  return {
    onClick: () => {
      handleRowClick(record);
    },
    style: {
      backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
      borderLeft: isSelected ? '3px solid #1890ff' : '3px solid transparent',
    },
    class: isSelected ? 'selected-row' : '',
  };
};

onMounted(() => {
  getTreeList();
});
</script>

<template>
  <ColPage auto-content-height v-bind="colPageProps">
    <template #left>
      <!-- 设备名称查询 -->
      <TreeSelect
        :checked-keys="treeValue"
        :data="treeList"
        :field-names="fieldNames"
        :leaf-only="true"
        :loading="deviceLoading"
        :show-icon="false"
        :show-search="true"
        :tree-default-expand-all="true"
        placeholder="请选择需要查询的设备"
        title="视频查询"
        @node-not-selectable="handleNodeNotSelectable"
        @select="handleSelect"
      >
        <template #extra>
          <!-- 日期选择器 -->
          <div class="mt-4">
            <div class="mb-2 text-sm font-medium text-gray-700">查询日期</div>
            <DatePicker
              v-model:value="selectedDate"
              class="w-full"
              placeholder="请选择日期"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </div>

          <!-- 查询按钮 -->
          <div class="mt-4">
            <Button class="w-full" type="primary" @click="searchVideos">
              查询
            </Button>
          </div>

          <!-- 视频列表 -->
          <div
            class=" mb-6 mt-4 max-h-[60%] min-h-[45%] w-full pr-2"
          >
            <div class="mb-2 flex items-center">
              <span class="text-sm font-medium text-gray-700">视频列表</span>
              <span
                v-if="deviceItems.length > 0"
                class="text-primary ml-2 text-xs"
              >
                已选择 {{ deviceItems.length }} 个视频
              </span>
            </div>
            <Table
              :columns="tableColumns"
              :custom-row="handleCustomRow"
              :data-source="deviceList"
              :loading="loading"
              :pagination="false"
              :scroll="{ y: 450 }"
              class="device-table"
              size="small"
            />
          </div>
        </template>
      </TreeSelect>
    </template>
    <div class="h-full flex-1 bg-white p-4">
      <template v-if="1">
        <GridView v-model="gridCount" @grid-change="handleGridChange">
          <template #default="{ index }">
            <div v-if="deviceItems[index]" class="relative h-full w-full">
              <div :class="`close-btn ${gridCount === 1 ? 'large-btn' : ''}`">
                <IconifyIcon
                  class="cursor-pointer text-lg"
                  icon="svg:close-circle-filled"
                  @click="removeVideo(index)"
                />
              </div>
              <VideoPlayer
                :key="`video-${deviceItems[index].id}`"
                :delay="index * 200"
                :download-file-name="deviceItems[index]?.name || ''"
                :enable-trim="true"
                :fit-container="true"
                :lazy="true"
                :show-controls="true"
                :url="deviceItems[index]?.filePath || ''"
                :visible="true"
                video-type="auto"
                @download-complete="handleDownloadComplete"
                @video-error="handleVideoError"
                @video-loaded="handleVideoLoaded"
              />
            </div>
            <Empty
              v-else
              class="flex h-full flex-col items-center justify-center"
              description="请从左侧选择相应设备以及查询时间"
            />
          </template>
        </GridView>
      </template>
      <Empty
        v-else
        class="flex h-full flex-col items-center justify-center"
        description="请从左侧选择相应设备以及查询时间"
      />
    </div>
  </ColPage>
</template>
<style scoped>
.device-table {
  :deep(.bd-table-cell) {
    font-size: 12px !important;
    padding: 4px 8px !important;
  }

  :deep(.bd-table-tbody > tr) {
    @apply cursor-pointer;
  }

  :deep(.bd-table-tbody > tr.selected-row) {
    background-color: #dbeafe !important;
  }

  :deep(.bd-table-tbody > tr.selected-row:hover) {
    background-color: #dbeafe !important;
  }
  :deep(.bd-table-tbody > tr.selected-row:hover > td) {
    background-color: #dbeafe !important;
  }

  :deep(.bd-pagination) {
    @apply mt-2;
  }

  :deep(.bd-pagination-item) {
    @apply text-xs;
  }

  :deep(.bd-pagination-options) {
    @apply text-xs;
  }

  :deep(.bd-pagination-total-text) {
    @apply text-xs text-gray-600;
  }
}

.close-btn {
  @apply absolute right-3 top-3 z-50;

  &.large-btn {
    :deep(.iconify--svg) {
      font-size: 24px !important;
    }
  }

  :deep(.iconify--svg) {
    font-size: 20px !important;
    background: #ccc;
    border-radius: 50%;
  }
}

/* 查询按钮样式 */
button:disabled {
  @apply cursor-not-allowed;
}
</style>
