<script lang="ts" setup>
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';

import { IconifyIcon } from '@bjy/icons';

import { Button, Progress, Select, Spin } from 'bhidi-design';

defineOptions({ name: 'VideoPlayerWithTrim' });

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  muted: true,
  fitContainer: true,
  heightClass: 'h-full',
  downloadFileName: 'video_trim',
  enableTrim: true,
  showControls: true,
  loading: false,
  enableFullscreen: true,
  enableVolumeControl: true,
  videoType: 'auto',
  lazy: true,
  visible: true,
  delay: 0,
});

const emit = defineEmits<{
  downloadComplete: [filename: string];
  trimEnd: [endTime: number];
  trimStart: [startTime: number];
  videoError: [error: Error];
  videoLoaded: [];
}>();

interface Props {
  url: string;
  autoplay?: boolean;
  muted?: boolean;
  fitContainer?: boolean;
  heightClass?: string;
  downloadFileName?: string;
  enableTrim?: boolean;
  showControls?: boolean;
  loading?: boolean;
  enableFullscreen?: boolean;
  enableVolumeControl?: boolean;
  videoType?: 'auto' | 'flv' | 'mp4';
  lazy?: boolean;
  visible?: boolean;
  delay?: number;
}

// 核心状态
const videoRef = ref<HTMLVideoElement | null>(null);
const containerRef = ref<HTMLDivElement | null>(null);
const progressBarRef = ref<HTMLDivElement | null>(null);

// 播放状态
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const buffered = ref(0);
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');

// 片段截取状态
const isDragging = ref(false);
const dragType = ref<'end' | 'start' | null>(null);
const trimStart = ref(0);
const trimEnd = ref(0);
const isTrimming = ref(false);
const trimProgress = ref(0);
const showTrimControls = ref(false); // 控制是否显示拖拽片段功能

// 媒体录制
const mediaRecorder = ref<MediaRecorder | null>(null);
const recordedChunks = ref<Blob[]>([]);
const isRecording = ref(false);

// 增强功能状态
const volume = ref(1);
const isMuted = ref(false);
const isFullscreen = ref(false);
const showVolumeSlider = ref(false);
const playbackRate = ref(1);

// FLV播放器支持
let flvjs: any = null;
let flvPlayer: any = null;

// 懒加载状态
const shouldLoad = ref(false);
const isInitialized = ref(false);
const isVisible = ref(false);

// 计算属性
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0;
  return (currentTime.value / duration.value) * 100;
});

const bufferedPercentage = computed(() => {
  if (duration.value === 0) return 0;
  return (buffered.value / duration.value) * 100;
});

const trimStartPercentage = computed(() => {
  if (duration.value === 0) return 0;
  return (trimStart.value / duration.value) * 100;
});

const trimEndPercentage = computed(() => {
  if (duration.value === 0) return 100;
  return (trimEnd.value / duration.value) * 100;
});

const trimDuration = computed(() => {
  return trimEnd.value - trimStart.value;
});

const canTrim = computed(() => {
  return (
    props.enableTrim &&
    duration.value > 0 &&
    !isRecording.value &&
    showTrimControls.value
  );
});

const canDownload = computed(() => {
  return recordedChunks.value.length > 0 && !isRecording.value;
});

// 懒加载检查
const checkShouldLoad = () => {
  if (!props.lazy) {
    shouldLoad.value = true;
    return;
  }
  shouldLoad.value = props.visible && isVisible.value;
};

// 可见性检测
let observer: IntersectionObserver | null = null;

const setupIntersectionObserver = () => {
  if (!containerRef.value) return;

  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        isVisible.value = entry.isIntersecting;
        checkShouldLoad();
      });
    },
    { threshold: 0.1 },
  );

  observer.observe(containerRef.value);
};

// FLV播放器初始化
const initFlvPlayer = async () => {
  if (props.videoType !== 'flv' && props.videoType !== 'auto') return;

  try {
    if (!flvjs) {
      const flvModule = await import('flv.js');
      flvjs = flvModule.default || flvModule;
    }

    if (!flvjs?.isSupported?.()) {
      throw new Error('当前环境不支持 flv.js');
    }

    if (!videoRef.value) return;

    if (flvPlayer) {
      flvPlayer.destroy();
      flvPlayer = null;
    }

    flvPlayer = flvjs.createPlayer({
      type: 'flv',
      url: props.url,
      isLive: true,
      enableWorker: false,
      enableStashBuffer: false,
      stashInitialSize: 1,
      autoCleanupSourceBuffer: true,
      autoCleanupMaxBackwardDuration: 0.3,
      autoCleanupMinBackwardDuration: 0.1,
      hasAudio: false,
      hasVideo: true,
    });

    flvPlayer.attachMediaElement(videoRef.value);
    flvPlayer.load();

    flvPlayer.on('error', (error: any) => {
      console.error('FLV播放错误:', error);
      hasError.value = true;
      errorMessage.value = 'FLV视频播放失败';
      isLoading.value = false;
      // 重置时长，避免显示异常值
      duration.value = 0;
      currentTime.value = 0;
    });
  } catch (error) {
    console.error('FLV播放器初始化失败:', error);
    hasError.value = true;
    errorMessage.value = 'FLV播放器初始化失败';
    isLoading.value = false;
    // 重置时长，避免显示异常值
    duration.value = 0;
    currentTime.value = 0;
  }
};

// 视频事件处理
const handleLoadedMetadata = () => {
  console.log('duration', videoRef.value?.duration);
  const videoDuration = videoRef.value?.duration;

  // 处理无效的duration值
  if (videoDuration && Number.isFinite(videoDuration) && videoDuration > 0) {
    duration.value = videoDuration;
  } else {
    duration.value = 0;
    console.warn('视频时长无效:', videoDuration);
  }

  trimEnd.value = duration.value;
  isLoading.value = false;
  hasError.value = false;
  emit('videoLoaded');
};

const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;

    if (videoRef.value.buffered.length > 0) {
      const bufferedEnd = videoRef.value.buffered.end(
        videoRef.value.buffered.length - 1,
      );
      buffered.value = bufferedEnd;
    }
  }
};

const handlePlay = () => {
  isPlaying.value = true;
};

const handlePause = () => {
  isPlaying.value = false;
};

const handleWaiting = () => {
  // 只有在没有错误的情况下才显示加载状态
  if (!hasError.value) {
    isLoading.value = true;
  }
};

const handleCanPlay = () => {
  isLoading.value = false;
};

const handleError = (_event: Event) => {
  const error = new Error('视频加载失败');
  hasError.value = true;
  errorMessage.value = '连接失败，请检查网络或者视频源';
  isLoading.value = false;
  // 重置时长，避免显示异常值
  duration.value = 0;
  currentTime.value = 0;
  emit('videoError', error);
};

// 播放控制
const togglePlay = () => {
  if (!videoRef.value) return;

  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }
};

const seekTo = (time: number) => {
  if (!videoRef.value) return;
  videoRef.value.currentTime = time;
};

// 前进后退功能 - 按照总时长的10%动态计算
const seekBackward = () => {
  if (!videoRef.value || duration.value <= 0) return;
  const seekStep = duration.value * 0.1; // 总时长的10%
  const newTime = Math.max(0, currentTime.value - seekStep);
  seekTo(newTime);
};

const seekForward = () => {
  if (!videoRef.value || duration.value <= 0) return;
  const seekStep = duration.value * 0.1; // 总时长的10%
  const newTime = Math.min(duration.value, currentTime.value + seekStep);
  seekTo(newTime);
};

// 进度条交互
const handleProgressClick = (event: MouseEvent) => {
  if (!progressBarRef.value || !videoRef.value || duration.value <= 0) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const percentage = Math.max(0, Math.min(1, clickX / rect.width));
  const time = percentage * duration.value;

  seekTo(time);
};

// 片段截取拖拽
const handleTrimMouseDown = (type: 'end' | 'start', event: MouseEvent) => {
  if (!canTrim.value) return;

  event.preventDefault();
  isDragging.value = true;
  dragType.value = type;

  document.addEventListener('mousemove', handleTrimMouseMove);
  document.addEventListener('mouseup', handleTrimMouseUp);
};

const handleTrimMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !progressBarRef.value || !dragType.value) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const mouseX = event.clientX - rect.left;
  const percentage = Math.max(0, Math.min(1, mouseX / rect.width));
  const time = percentage * duration.value;

  if (dragType.value === 'start') {
    trimStart.value = Math.min(time, trimEnd.value - 0.1);
  } else {
    trimEnd.value = Math.max(time, trimStart.value + 0.1);
  }
};

const handleTrimMouseUp = () => {
  isDragging.value = false;
  dragType.value = null;

  document.removeEventListener('mousemove', handleTrimMouseMove);
  document.removeEventListener('mouseup', handleTrimMouseUp);
};

// 触摸事件支持
const handleTrimTouchStart = (type: 'end' | 'start', event: TouchEvent) => {
  if (!canTrim.value) return;

  event.preventDefault();
  isDragging.value = true;
  dragType.value = type;

  document.addEventListener('touchmove', handleTrimTouchMove, {
    passive: false,
  });
  document.addEventListener('touchend', handleTrimTouchEnd);
};

const handleTrimTouchMove = (event: TouchEvent) => {
  if (!isDragging.value || !progressBarRef.value || !dragType.value) return;

  event.preventDefault();
  const touch = event.touches[0];
  if (!touch) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const touchX = touch.clientX - rect.left;
  const percentage = Math.max(0, Math.min(1, touchX / rect.width));
  const time = percentage * duration.value;

  if (dragType.value === 'start') {
    trimStart.value = Math.min(time, trimEnd.value - 0.1);
  } else {
    trimEnd.value = Math.max(time, trimStart.value + 0.1);
  }
};

const handleTrimTouchEnd = () => {
  isDragging.value = false;
  dragType.value = null;

  document.removeEventListener('touchmove', handleTrimTouchMove);
  document.removeEventListener('touchend', handleTrimTouchEnd);
};

// 片段控制
const activateTrimMode = () => {
  showTrimControls.value = true;
  trimStart.value = 0;
  trimEnd.value = duration.value;
};

const resetTrim = () => {
  trimStart.value = 0;
  trimEnd.value = duration.value;
};

const goToTrimStart = () => {
  seekTo(trimStart.value);
};

const goToTrimEnd = () => {
  seekTo(trimEnd.value);
};

// 音量控制
const toggleMute = () => {
  if (!videoRef.value) return;

  isMuted.value = !isMuted.value;
  videoRef.value.muted = isMuted.value;

  if (!isMuted.value) {
    videoRef.value.volume = volume.value;
  }
};

const setVolume = (newVolume: number) => {
  volume.value = Math.max(0, Math.min(1, newVolume));
  if (videoRef.value && !isMuted.value) {
    videoRef.value.volume = volume.value;
  }
};

// 全屏控制
const toggleFullscreen = async () => {
  if (!containerRef.value) return;

  try {
    if (isFullscreen.value) {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      }
    } else {
      if (containerRef.value.requestFullscreen) {
        await containerRef.value.requestFullscreen();
      }
    }
  } catch (error) {
    console.error('全屏切换失败:', error);
  }
};

// 播放速度控制
const setPlaybackRate = (rate: number | string) => {
  const rateValue = typeof rate === 'string' ? Number.parseFloat(rate) : rate;
  playbackRate.value = rateValue;
  if (videoRef.value) {
    videoRef.value.playbackRate = rateValue;
  }
};

// 激活片段选择模式
const startTrimMode = () => {
  if (!videoRef.value || !props.enableTrim || duration.value <= 0) return;
  activateTrimMode();
};

// 真正的剪切和下载功能
const startRecording = async () => {
  if (!videoRef.value || !showTrimControls.value) return;

  try {
    isRecording.value = true;
    isTrimming.value = true;
    trimProgress.value = 0;

    // 跳转到片段开始
    seekTo(trimStart.value);

    await new Promise((resolve) => {
      const onSeeked = () => {
        videoRef.value?.removeEventListener('seeked', onSeeked);
        resolve(void 0);
      };
      videoRef.value?.addEventListener('seeked', onSeeked);
    });

    // 获取视频流
    const stream =
      (videoRef.value as any).captureStream?.() ||
      (videoRef.value as any).mozCaptureStream?.();

    if (!stream) {
      throw new Error('无法获取视频流');
    }

    const mimeType = MediaRecorder.isTypeSupported('video/mp4;codecs=avc1')
      ? 'video/mp4;codecs=avc1'
      : 'video/webm;codecs=vp9';

    mediaRecorder.value = new MediaRecorder(stream, { mimeType });
    recordedChunks.value = [];

    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunks.value.push(event.data);
      }
    };

    mediaRecorder.value.onstop = () => {
      isRecording.value = false;
      isTrimming.value = false;
      downloadRecordedVideo();
    };

    // 开始录制
    mediaRecorder.value.start();
    videoRef.value.play();

    // 监控录制进度
    const progressInterval = setInterval(() => {
      if (videoRef.value && isRecording.value) {
        const progress =
          ((videoRef.value.currentTime - trimStart.value) /
            trimDuration.value) *
          100;
        trimProgress.value = Math.min(100, Math.max(0, progress));

        // 到达片段结束时停止录制
        if (videoRef.value.currentTime >= trimEnd.value) {
          stopRecording();
          clearInterval(progressInterval);
        }
      }
    }, 100);
  } catch (error) {
    console.error('开始剪切失败:', error);
    isRecording.value = false;
    isTrimming.value = false;
  }
};

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
  }
};

const downloadRecordedVideo = () => {
  if (recordedChunks.value.length === 0) return;

  const blob = new Blob(recordedChunks.value, { type: 'video/mp4' });
  const url = URL.createObjectURL(blob);

  const filename = `${props.downloadFileName}_${Date.now()}.mp4`;
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();

  URL.revokeObjectURL(url);
  emit('downloadComplete', filename);

  recordedChunks.value = [];
  trimProgress.value = 0;
};

// 格式化时间
const formatTime = (time: number) => {
  // 处理无效值：NaN, Infinity, 负数
  if (!Number.isFinite(time) || time < 0) {
    return '00:00';
  }

  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (!videoRef.value) return;

  switch (event.code) {
    case 'ArrowLeft': {
      event.preventDefault();
      seekBackward();
      break;
    }
    case 'ArrowRight': {
      event.preventDefault();
      seekForward();
      break;
    }
    case 'Escape': {
      if (isFullscreen.value) {
        event.preventDefault();
        toggleFullscreen();
      }
      break;
    }
    case 'KeyF': {
      event.preventDefault();
      if (props.enableFullscreen) {
        toggleFullscreen();
      }
      break;
    }
    case 'KeyG': {
      event.preventDefault();
      if (canTrim.value && !isRecording.value) {
        startRecording();
      }
      break;
    }
    case 'KeyJ': {
      event.preventDefault();
      seekBackward();
      break;
    }
    case 'KeyL': {
      event.preventDefault();
      seekForward();
      break;
    }
    case 'KeyM': {
      event.preventDefault();
      toggleMute();
      break;
    }
    case 'Space': {
      event.preventDefault();
      togglePlay();
      break;
    }
  }
};

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
};

// 初始化视频播放器
const initVideoPlayer = async () => {
  if (!videoRef.value || !shouldLoad.value) return;

  if (isInitialized.value) return;

  try {
    // 只有在没有错误的情况下才显示加载状态
    if (!hasError.value) {
      isLoading.value = true;
    }
    isInitialized.value = true;

    videoRef.value.volume = volume.value;
    videoRef.value.muted = isMuted.value;

    if (
      props.videoType === 'flv' ||
      (props.videoType === 'auto' && props.url.includes('.flv'))
    ) {
      await initFlvPlayer();
    }
  } catch (error) {
    console.error('视频播放器初始化失败:', error);
    isLoading.value = false;
  }
};

// 清理播放器
const cleanupPlayer = () => {
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
  isInitialized.value = false;
};

// 生命周期
onMounted(async () => {
  document.addEventListener('keydown', handleKeydown);
  document.addEventListener('fullscreenchange', handleFullscreenChange);

  if (props.lazy) {
    setupIntersectionObserver();
  } else {
    shouldLoad.value = true;
    await nextTick();
    initVideoPlayer();
  }
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);

  if (observer) {
    observer.disconnect();
  }

  stopRecording();
  cleanupPlayer();
});

// 监听可见性变化
watch(
  () => props.visible,
  () => {
    if (props.lazy) {
      checkShouldLoad();
      if (shouldLoad.value && !isInitialized.value) {
        initVideoPlayer();
      }
    }
  },
);

// 监听懒加载状态
watch(shouldLoad, (newShouldLoad) => {
  if (newShouldLoad && !isInitialized.value) {
    nextTick(() => {
      initVideoPlayer();
    });
  }
});

// 监听外部加载状态
watch(
  () => props.loading,
  (newLoading) => {
    if (newLoading) {
      isLoading.value = true;
      hasError.value = false;
    } else {
      // 当外部loading为false时，确保清除加载状态
      isLoading.value = false;
    }
  },
);
</script>

<template>
  <div
    ref="containerRef"
    :class="[props.fitContainer ? 'flex-1' : '']"
    class="relative flex h-full w-full flex-col overflow-hidden rounded-lg border border-gray-200 bg-black"
  >
    <!-- 视频元素 -->
    <div class="relative flex-1 overflow-hidden">
      <video
        ref="videoRef"
        :autoplay="props.autoplay"
        :class="[
          props.heightClass,
          props.fitContainer ? 'h-full w-full object-contain' : 'w-full',
        ]"
        :muted="props.muted"
        :src="props.url"
        class="w-full"
        playsinline
        @canplay="handleCanPlay"
        @error="handleError"
        @loadedmetadata="handleLoadedMetadata"
        @pause="handlePause"
        @play="handlePlay"
        @timeupdate="handleTimeUpdate"
        @waiting="handleWaiting"
      ></video>

      <!-- 加载遮罩 -->
      <div
        v-if="(isLoading || props.loading) && !hasError"
        class="absolute inset-0 z-10 flex items-center justify-center bg-black/50"
      >
        <div class="flex flex-col items-center gap-4">
          <Spin size="large" />
          <span class="text-white">视频加载中...</span>
        </div>
      </div>

      <!-- 错误状态 -->
      <div
        v-if="hasError"
        class="absolute inset-0 z-10 flex items-center justify-center bg-black/50"
      >
        <div class="flex flex-col items-center gap-4 text-white">
          <div class="text-center">
            <div class="text-lg font-semibold">视频加载失败</div>
            <div class="mt-1 text-sm text-gray-300">{{ errorMessage }}</div>
          </div>
        </div>
      </div>

      <!-- 录制进度遮罩 -->
      <div
        v-if="isTrimming"
        class="absolute inset-0 z-10 flex items-center justify-center bg-black/70"
      >
        <div class="flex flex-col items-center gap-4 text-white">
          <div class="animate-spin text-4xl">🎬</div>
          <div class="text-center">
            <div class="text-lg font-semibold">正在生成片段...</div>
            <div class="text-sm text-gray-300">
              {{ formatTime(trimStart) }} - {{ formatTime(trimEnd) }}
            </div>
            <div class="mt-2 w-64">
              <Progress :percent="trimProgress" />
            </div>
          </div>
        </div>
      </div>

      <!-- 悬浮控制按钮 -->
      <div class="absolute left-2 top-2 z-20 flex flex-col gap-2"></div>
    </div>

    <!-- 控制栏 -->
    <div
      v-if="props.showControls"
      class="flex flex-col gap-1 bg-[#f5f5f5] px-2 pb-1 pt-2"
    >
      <!-- 进度条 -->
      <div class="relative">
        <div
          ref="progressBarRef"
          class="relative h-2 w-full cursor-pointer rounded-full bg-gray-600"
          @click="handleProgressClick"
        >
          <!-- 缓冲进度 -->
          <div
            :style="{ width: `${bufferedPercentage}%` }"
            class="absolute left-0 top-0 h-full rounded-full bg-gray-500"
          ></div>

          <!-- 播放进度 -->
          <div
            :style="{ width: `${progressPercentage}%` }"
            class="absolute left-0 top-0 h-full rounded-full bg-blue-500"
          ></div>

          <!-- 片段选择区域 - 只在激活片段选择模式时显示 -->
          <div
            v-if="showTrimControls"
            :style="{
              left: `${trimStartPercentage}%`,
              width: `${trimEndPercentage - trimStartPercentage}%`,
            }"
            class="absolute top-0 h-full rounded-full bg-green-500/30"
          ></div>

          <!-- 片段开始拖拽点 -->
          <div
            v-if="showTrimControls"
            :style="{ left: `calc(${trimStartPercentage}% - 8px)` }"
            class="absolute top-1/2 h-4 w-4 -translate-y-1/2 cursor-ew-resize touch-none select-none rounded-full border-2 border-green-400 bg-green-500"
            @mousedown="handleTrimMouseDown('start', $event)"
            @touchstart="handleTrimTouchStart('start', $event)"
          ></div>

          <!-- 片段结束拖拽点 -->
          <div
            v-if="showTrimControls"
            :style="{ left: `calc(${trimEndPercentage}% - 8px)` }"
            class="absolute top-1/2 h-4 w-4 -translate-y-1/2 cursor-ew-resize touch-none select-none rounded-full border-2 border-green-400 bg-green-500"
            @mousedown="handleTrimMouseDown('end', $event)"
            @touchstart="handleTrimTouchStart('end', $event)"
          ></div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="flex items-center justify-between text-xs">
        <div class="flex items-center gap-2">
          <span>{{ formatTime(currentTime) }}</span>
          <span class="text-gray-400">/</span>
          <span class="text-gray-400">{{ formatTime(duration) }}</span>
        </div>

        <div
          v-if="showTrimControls"
          class="flex items-center gap-2 text-green-400"
        >
          <span>
            片段: {{ formatTime(trimStart) }} - {{ formatTime(trimEnd) }}
          </span>
          <span class="text-xs">({{ formatTime(trimDuration) }})</span>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <!-- 前进控制 -->
          <Button
            :disabled="!videoRef"
            class="player-button"
            ghost
            type="text"
            @click="seekBackward"
          >
            <IconifyIcon icon="svg:audio-forward" />
          </Button>
          <!-- 播放/暂停 -->
          <Button
            :disabled="!videoRef"
            class="player-button-large"
            ghost
            type="text"
            @click="togglePlay"
          >
            <IconifyIcon
              :icon="isPlaying ? 'svg:audio-pause' : 'svg:audio-play'"
            />
          </Button>
          <!-- 后退控制 -->
          <Button
            :disabled="!videoRef"
            class="player-button"
            ghost
            size="middle"
            type="text"
            @click="seekForward"
          >
            <IconifyIcon icon="svg:audio-backward" />
          </Button>
          <!-- 音量控制 -->
          <div
            v-if="props.enableVolumeControl"
            class="relative flex items-center gap-2"
          >
            <div
              v-show="showVolumeSlider"
              class="absolute bottom-full left-0 mb-2 w-20"
              @mouseenter="showVolumeSlider = true"
              @mouseleave="showVolumeSlider = false"
            >
              <input
                v-model="volume"
                :max="1"
                :min="0"
                :step="0.1"
                class="w-full"
                type="range"
                @input="
                  (e) => setVolume(Number((e.target as HTMLInputElement).value))
                "
              />
            </div>
          </div>

          <!-- 片段控制 -->
          <!-- <template v-if="canTrim">
            <Button size="small" variant="ghost" @click="goToTrimStart">
            </Button>

            <Button size="small" variant="ghost" @click="goToTrimEnd">
            </Button>

            <Button size="small" variant="ghost" @click="resetTrim">
            </Button>
          </template> -->
        </div>

        <!-- 右侧控制按钮 -->
        <div class="flex items-center gap-1">
          <!-- 激活片段选择按钮 -->
          <Button
            v-if="!showTrimControls && props.enableTrim && duration > 0"
            size="small"
            type="primary"
            @click="startTrimMode"
          >
            选择片段
          </Button>

          <!-- 片段选择模式下的按钮 -->
          <template v-if="showTrimControls">
            <Button
              v-if="!isRecording"
              :disabled="trimDuration <= 0"
              size="small"
              type="primary"
              @click="startRecording"
            >
              剪切
            </Button>

            <Button
              v-else
              class="shadow-lg"
              size="small"
              type="primary"
              @click="stopRecording"
            >
              停止
            </Button>

            <Button
              v-if="canDownload"
              size="small"
              type="default"
              @click="downloadRecordedVideo"
            >
              下载
            </Button>

            <Button
              size="small"
              type="default"
              @click="showTrimControls = false"
            >
              取消
            </Button>
          </template>
          <!-- 播放速度 -->
          <div class="flex items-center gap-1">
            <Select
              v-model="playbackRate"
              :options="[
                { label: '2x', value: 2 },
                { label: '1.5x', value: 1.5 },
                { label: '1.25x', value: 1.25 },
                { label: '1x', value: 1 },
                { label: '0.75x', value: 0.75 },
                { label: '0.5x', value: 0.5 },
              ]"
              class="w-18"
              placeholder="倍速"
              size="small"
              @change="setPlaybackRate"
            />
          </div>
          <!-- 静音控制 -->
          <Button
            class="player-button"
            ghost
            size="small"
            type="text"
            @click="toggleMute"
          >
            <IconifyIcon
              :icon="isMuted ? 'svg:audio-muted' : 'svg:audio-sound'"
            />
          </Button>
          <!-- 全屏 -->
          <Button
            v-if="props.enableFullscreen"
            class="player-button text-primary"
            ghost
            size="small"
            type="text"
            @click="toggleFullscreen"
          >
            <IconifyIcon v-if="isFullscreen" icon="svg:fullscreen-exit" />
            <IconifyIcon v-else icon="svg:fullsreen" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 拖拽时的样式 */
.dragging {
  cursor: ew-resize !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .text-sm {
    font-size: 0.75rem;
  }

  .h-4 {
    height: 0.875rem;
  }

  .w-4 {
    width: 0.875rem;
  }

  /* 移动端触摸优化 */
  .touch-none {
    touch-action: none;
  }

  /* 移动端按钮间距 */
  .flex.gap-2 {
    gap: 0.25rem;
  }

  /* 移动端控制栏 */
  .bg-gray-900 {
    padding: 0.5rem;
  }

  /* 移动端拖拽点 */
  .h-4.w-4 {
    height: 1rem;
    width: 1rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover\:bg-muted:hover {
    background-color: transparent;
  }

  /* 增大触摸目标 */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* 拖拽点增大 */
  .h-4.w-4 {
    height: 1.5rem;
    width: 1.5rem;
  }
}

/* 全屏模式样式 */
:fullscreen .bg-gray-900 {
  background-color: rgba(0, 0, 0, 0.8);
}

:-webkit-full-screen .bg-gray-900 {
  background-color: rgba(0, 0, 0, 0.8);
}

:-moz-full-screen .bg-gray-900 {
  background-color: rgba(0, 0, 0, 0.8);
}

:-ms-fullscreen .bg-gray-900 {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 播放器按钮样式 */
.player-button {
  @apply !h-auto !px-1;
  .iconify--svg {
    font-size: 18px;
  }
}
.player-button-large {
  @apply !h-auto !px-1;
  .iconify--svg {
    font-size: 24px;
  }
}

/* 悬浮按钮在移动端的优化 */
@media (max-width: 640px) {
  .absolute.right-2.top-2 {
    right: 0.5rem;
    top: 0.5rem;
  }

  .absolute.right-2.top-2 button {
    min-height: 36px;
    min-width: 36px;
    font-size: 0.75rem;
  }
}
</style>
