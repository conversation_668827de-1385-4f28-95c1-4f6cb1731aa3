<script lang="ts" setup>
import { computed } from 'vue';

import { useAntdDesignTokens } from '@bjy/hooks';
import { preferences, usePreferences } from '@bjy/preferences';

import { App, ConfigProvider, theme, Button } from 'bhidi-design';
import { StyleProvider } from 'ant-design-vue';
import { legacyLogicalPropertiesTransformer } from 'ant-design-vue/es/style';

import { antdLocale } from '#/locales';

defineOptions({ name: 'App' });

const { isDark } = usePreferences();
const { tokens } = useAntdDesignTokens();
const rootStyles = getComputedStyle(document.documentElement);
const getCssVariableValue = (variable: string) => {
  const value = rootStyles.getPropertyValue(variable);
  return `hsl(${value})`;
};

const tokenTheme = computed(() => {
  const algorithm = isDark.value
    ? [theme.darkAlgorithm]
    : [theme.defaultAlgorithm];

  // antd 紧凑模式算法
  if (preferences.app.compact) {
    algorithm.push(theme.compactAlgorithm);
  }

  return {
    algorithm,
    token: {
      ...tokens,
      colorBgContainerDisabled: '#f3f3f3',
      colorTextDisabled: '#ccc',
      colorTextPlaceholder: getCssVariableValue('--color-text-placeholder'),
      controlItemBgActive: getCssVariableValue(
        '--bjy-menu-item-active-background-color',
      ),
      controlItemBgHover: getCssVariableValue('--accent'),
      colorPrimary: getCssVariableValue('--primary'),
    },
  };
});
</script>

<template>
  <ConfigProvider :locale="antdLocale" :theme="tokenTheme">
    <StyleProvider :transformers="[legacyLogicalPropertiesTransformer]">
      <App>
        <!-- <RouterView /> -->
        <Button type="primary">123</Button>
      </App>
    </StyleProvider>
  </ConfigProvider>
</template>
