<script lang="ts" setup>
import { Page, type VbenFormProps, useVbenModal } from '@bjy/common-ui';
import dayjs from 'dayjs';

import { useVbenVxeGrid, type VxeGridProps } from '#/adapter/vxe-table';
import {
  Button,
  Popconfirm,
  Badge,
  Tabs,
  TabPane,
  message,
} from 'bhidi-design';
import {
  businessColumns,
  searchFormSchema,
  businessEmailFormSchema,
  businessSmsFormSchema,
  BusinesEventDetailsFormSchema,
} from './data.js';
import { useVbenForm } from '#/adapter/form';
import AttachmentPreview from './AttachmentPreview.vue';
import { getStatusText, getStatusTextColor } from '#/util/status.js';
import { ref, reactive, nextTick, watch } from 'vue';
import {
  getPoleDeviceEventPage,
  putPoleDeviceEventUpdate,
  DeletePoleDeviceEventDelete,
  postPoleDeviceEventDisposal,
  postPoleDeviceEventUpdateStatus,
} from '#/api/eventManagement/business.js';

defineOptions({ name: 'IotTemplate' });

// 查询条件
const formOptions: VbenFormProps = {
  schema: searchFormSchema,
  showCollapseButton: false,
  commonConfig: {
    labelWidth: 65,
  },
};

const gridOptions: VxeGridProps<any> = {
  columnConfig: {
    resizable: false,
  },
  columns: businessColumns,
  editConfig: {
    mode: 'cell',
    trigger: 'dblclick',
  },
  height: 'auto',
  pagerConfig: {
    pageSize: 10,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const data = await getPoleDeviceEventPage({
          pageNo: page.currentPage,
          pageSize: page.pageSize,
          eventType: 0,
          ...formValues,
        });
        return { ...data, items: data.list };
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: false,
    zoom: false,
  },
  editRules: {
    remark: [{ required: false, message: '备注不能为空' }],
  },
  // 编辑事件处理
  onEditClosed: async ({ row, column }) => {
    // 只处理备注字段的编辑
    if (column.field === 'remark') {
      try {
        // 调用更新接口
        await putPoleDeviceEventUpdate(row);
        GridApi.reload();
        message.success('更新成功');
      } catch (error) {
        message.error('更新失败');
        // 可以选择重新加载数据以恢复原始值
        GridApi.reload();
      }
    }
  },
};
const selectedRows = ref<any[]>([]);
const [Grid, GridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: (e: Record<string, any>) => {
      selectedRows.value = e.records;
    },
    checkboxAll: (e: Record<string, any>) => {
      selectedRows.value = e.records;
    },
  },
});
// 发送方式切换
const sendType = ref<'email' | 'sms'>('sms');

// 当前处置的事件数据
const currentDisposalRow = ref<any>(null);

// 表单数据缓存 - 使用响应式数据直接绑定
const emailFormData = reactive({
  email: '',
  subject: '',
  content: '',
});

const smsFormData = reactive({
  phone: '',
  content: '',
});

//  邮件表单
const [businessEmailForm, businessEmailApiForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 100,
  },
  schema: businessEmailFormSchema,
  showDefaultActions: false,
});

//  短信表单
const [businessSmsForm, businessSmsApiForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 100,
  },
  schema: businessSmsFormSchema,
  showDefaultActions: false,
});

// 实时监听表单数据变化并保存到缓存
let emailFormWatcher: any = null;
let smsFormWatcher: any = null;
// 启动表单数据监听
const startFormWatching = () => {
  // 监听邮件表单
  if (sendType.value === 'email' && !emailFormWatcher) {
    emailFormWatcher = watch(
      () => {
        try {
          return businessEmailApiForm.getValues();
        } catch {
          return {};
        }
      },
      (newValues) => {
        if (newValues && typeof newValues === 'object') {
          emailFormData.email = newValues.email || '';
          emailFormData.subject = newValues.subject || '';
          emailFormData.content = newValues.content || '';
        }
      },
      { deep: true, immediate: false },
    );
  }

  // 监听短信表单
  if (sendType.value === 'sms' && !smsFormWatcher) {
    smsFormWatcher = watch(
      () => {
        try {
          return businessSmsApiForm.getValues();
        } catch {
          return {};
        }
      },
      (newValues) => {
        if (newValues && typeof newValues === 'object') {
          smsFormData.phone = newValues.phone || '';
          smsFormData.content = newValues.content || '';
        }
      },
      { deep: true, immediate: false },
    );
  }
};

// 停止表单数据监听
const stopFormWatching = () => {
  if (emailFormWatcher) {
    emailFormWatcher();
    emailFormWatcher = null;
  }
  if (smsFormWatcher) {
    smsFormWatcher();
    smsFormWatcher = null;
  }
};

// 监听sendType变化，在组件重新创建后恢复数据
watch(
  sendType,
  async (newType) => {
    // 停止之前的监听
    stopFormWatching();

    // 等待组件重新渲染
    await nextTick();
    await nextTick(); // 多等一个tick确保组件完全创建

    // 恢复对应表单的缓存数据
    const targetForm =
      newType === 'email' ? businessEmailApiForm : businessSmsApiForm;
    const cachedData = newType === 'email' ? emailFormData : smsFormData;

    // 检查缓存中是否有数据
    const hasData = Object.values(cachedData).some((value) => value !== '');

    if (hasData) {
      targetForm.setValues(cachedData);
    }

    // 启动新的表单监听
    startFormWatching();
  },
  { immediate: true },
);
const [businessModal, businessApiModal] = useVbenModal({
  title: '事件处置',
  class: 'w-[600px]',
  contentClass: 'min-h-max pb-0',
  closeOnClickModal: false,
  confirmText: '发送',
  onCancel() {
    businessApiModal.close();
  },

  onOpenChange: async (isOpen: boolean) => {
    if (!isOpen) {
      // 关闭时清空缓存并重置表单
      Object.assign(emailFormData, {
        email: '',
        subject: '',
        content: '',
      });
      Object.assign(smsFormData, { phone: '', content: '' });
      businessEmailApiForm.resetForm();
      businessSmsApiForm.resetForm();
      // 重置为默认的短信发送模式
      sendType.value = 'sms';
    }
  },

  onConfirm: async () => {
    try {
      const currentForm =
        sendType.value === 'email' ? businessEmailApiForm : businessSmsApiForm;
      const values = await currentForm.validate();
      let res = await currentForm.getValues();

      if (values) {
        businessApiModal.setState({
          confirmLoading: true,
        });

        // 调用事件处置接口
        const disposalParams = {
          sendType: sendType.value,
          ...currentDisposalRow.value,
          ...res,
        };

        await postPoleDeviceEventDisposal(disposalParams);
        message.success('事件处置成功');

        // 刷新表格数据
        GridApi.reload();

        // 提交成功后清空缓存并重置表单
        Object.assign(emailFormData, {
          email: '',
          subject: '',
          content: '',
        });
        Object.assign(smsFormData, { phone: '', content: '' });
        businessEmailApiForm.resetForm();
        businessSmsApiForm.resetForm();
        sendType.value = 'sms';
        businessApiModal.setState({
          confirmLoading: false,
        });
        businessApiModal.close();
        GridApi.reload();
      }
    } catch (error) {
      businessApiModal.setState({
        confirmLoading: false,
      });
    
    }
  },
});
// 事件名称预览
const [BusinesEventDetailsForm, BusinesEventDetailsApiForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 100,
  },
  schema: BusinesEventDetailsFormSchema,
  showDefaultActions: false,
});
const [BusinesEventDetailsModal, BusinesEventDetailsApiModal] = useVbenModal({
  title: '业务事件详情',
  class: 'w-[800px]',
  contentClass: 'max-h-[60vh] overflow-y-auto pb-0',
  closeOnClickModal: false,
  onCancel() {
    BusinesEventDetailsApiModal.close();
  },

  onOpenChange: async (isOpen: boolean) => {},

  onConfirm: async () => {
    try {
      // 验证表单
      const isValid = await BusinesEventDetailsApiForm.validate();
      if (!isValid) {
        return;
      }
      BusinesEventDetailsApiModal.setState({
        confirmLoading: true,
      });
      let params = await BusinesEventDetailsApiForm.getValues();
      await putPoleDeviceEventUpdate({ ...params });
      message.success('操作成功');

      GridApi.reload();
      BusinesEventDetailsApiModal.close();
      BusinesEventDetailsApiModal.setState({
        confirmLoading: false,
      });
    } catch (error) {}
  },
});

// 处置事件
const handleDispose = (row: any) => {
  // 保存当前处置的事件数据
  currentDisposalRow.value = row;

  // 清空缓存
  Object.assign(emailFormData, { email: '', subject: '', content: '' });
  Object.assign(smsFormData, { phone: '', content: '' });

  // 重置为短信发送模式
  sendType.value = 'sms';

  // 设置邮件表单的默认值并同步到缓存
  const emailDefaults = {
    ...row,
    email: row.email || '',
    subject: `${row.eventName} - ${row.monitorDevice}`,
    content: '',
  };

  const smsDefaults = {
    ...row,
    phone: row.phone || '',
    content: '',
  };

  businessEmailApiForm.setValues(emailDefaults);
  businessSmsApiForm.setValues(smsDefaults);

  // 同步到缓存
  Object.assign(emailFormData, emailDefaults);
  Object.assign(smsFormData, smsDefaults);

  businessApiModal.open();
  if (row.status === 0) {
    postPoleDeviceEventUpdateStatus(row.id);
  }
  GridApi.reload();
};

// 切换发送方式
const handleSendTypeChange = (activeKey: 'email' | 'sms') => {
  // 切换发送方式（watch会自动处理数据保存和恢复）
  sendType.value = activeKey;
};
// 删除事件
const handleDelete = async (_row: any, type: string) => {
  try {
    if (type == 'all') {
      // 字符串拼接删除多个事件
      let ids = selectedRows.value.map((item: any) => item.id).join(',');
      await DeletePoleDeviceEventDelete({ ids });
      message.success('删除成功');
      GridApi.reload();
      selectedRows.value = [];
    } else {
      let res = await DeletePoleDeviceEventDelete({ ids: [_row.id] });
      message.success('删除成功');
      GridApi.reload();
    }
  } catch (error) {}
};
// 查看事件详情
const handleEventDetails = (row: any) => {
  // 格式化时间字段后设置表单数据
  const formattedRow = {
    ...row,
    happenTime: row.createTime
      ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  BusinesEventDetailsApiForm.setValues(formattedRow);
  BusinesEventDetailsApiModal.open();
  if (row.status === 0) {
    postPoleDeviceEventUpdateStatus(row.id);
  }
  GridApi.reload();
};
const AttachmentPreviewStatusFn = (row: any) => {
  if (row.status === 0) {
    postPoleDeviceEventUpdateStatus(row.id);
    GridApi.reload();
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <div>
          <span style="font-size: 16px; font-weight: bold">业务事件列表</span>
        </div>
      </template>
      <template #toolbar-tools>
        <Popconfirm
          placement="left"
          title="是否要删除该事件？"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRows.length === 0"
          @confirm="() => handleDelete(undefined, 'all')"
        >
          <Button type="primary" :disabled="selectedRows.length === 0"
            >批量删除</Button
          >
        </Popconfirm>
      </template>
      <template #eventName="{ row }">
        <span
          class="cursor-pointer text-red-500 hover:text-red-600 hover:underline"
          @click="handleEventDetails(row)"
          >{{ row.eventName }}</span
        >
      </template>
      <template #attachment="{ row }">
        <AttachmentPreview
          :value="row.attachment"
          :show-first-only="true"
          :row="row"
          @AttachmentPreviewStatusFn="AttachmentPreviewStatusFn"
        />
      </template>
      <template #status="{ row }">
        <Badge
          :text="getStatusText(row?.status)"
          :color="getStatusTextColor(row?.status)"
        ></Badge>
      </template>
      <template #action="{ row }">
        <Button class="px-2" type="link" @click="handleDispose(row)">
          处置
        </Button>
        <Popconfirm
          placement="left"
          title="是否要删除该事件？"
          ok-text="确定"
          cancel-text="取消"
          @confirm="() => handleDelete(row, 'single')"
        >
          <Button class="px-2" type="link"> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
    <businessModal>
      <!-- 发送方式切换 -->
      <Tabs v-model:active-key="sendType" @change="handleSendTypeChange">
        <TabPane key="sms">
          <template #tab>
            <span>发送短信</span>
          </template>
        </TabPane>
        <TabPane key="email">
          <template #tab>
            <span>发送邮件</span>
          </template>
        </TabPane>
      </Tabs>

      <!-- 表单内容 -->
      <businessSmsForm v-if="sendType === 'sms'" />
      <businessEmailForm v-if="sendType === 'email'" />
    </businessModal>
    <BusinesEventDetailsModal>
      <BusinesEventDetailsForm>
        <template #attachment="{ value }">
          <AttachmentPreview :value="value" />
        </template>
      </BusinesEventDetailsForm>
    </BusinesEventDetailsModal>
  </Page>
</template>
<style scoped></style>
