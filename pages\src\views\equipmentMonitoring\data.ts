import type { VxeGridProps } from '@bjy/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
    fixed: 'left',
  },
  { title: '序号', type: 'seq', width: 60 },
  {
    field: 'deviceSerial',
    title: '序列号',
    minWidth: 150,
  },
  {
    field: 'deviceName',
    title: '设备名称',
    minWidth: 120,
  },
  {
    field: 'linkState',
    title: '是否在线',
    minWidth: 100,
    slots: { default: 'linkState' },
  },
  {
    field: 'powerLevel',
    title: '电量(%)',
    minWidth: 100,
    slots: { default: 'powerLevel' },
  },
  {
    field: 'deviceSignal',
    title: '信号(dBm)',
    minWidth: 100,
    slots: { default: 'deviceSignal' },
  },
  {
    field: 'deviceVoltage',
    title: '电压(V)',
    minWidth: 100,
  },
  {
    field: 'devicePosition',
    slots: { default: 'devicePosition' },
    title: '位置 (经度°,维度°,高度m)',
    minWidth: 220,
  },
  {
    field: 'hornState',
    slots: { default: 'hornState' },
    title: '喇叭控制',
    width: 100,
    align: 'center',
  },
  {
    field: 'rblState',
    slots: { default: 'rblState' },
    title: '红蓝灯控制',
    width: 110,
    align: 'center',
  },
  {
    field: 'ledState',
    slots: { default: 'ledState' },
    title: 'LED控制',
    width: 100,
    align: 'center',
  },
];

export const searchFormSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入设备名称',
    },
    fieldName: 'deviceName',
    label: '设备名称',
  },
  {
    component: 'Input',
    componentProps: {
      autocomplete: 'off',
      maxlength: 32,
      placeholder: '请输入序列号',
    },
    fieldName: 'deviceSerial',
    label: '序列号',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: null },
        { label: '在线', value: 2 },
        { label: '离线', value: 1 },
      ],
    },
    fieldName: 'linkState',
    label: '是否在线',
  },
];
